interface FileImport {
  id: number;
  filename: string;
  file_type: string;
  upload_date: string;
  uploaded_by: string;
  status: string;
  records_count: number;
}

interface PaginatedResponse<T> {
  data: T[];
  total: number;
}

class LocalStorageService {
  private static STORAGE_KEY = 'mock_file_imports';

  static loadData(): FileImport[] {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : this.getDefaultData();
    } catch {
      // Silently fall back to default data if localStorage fails
      return this.getDefaultData();
    }
  }

  static saveData(data: FileImport[]): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch {
      // Silently ignore localStorage errors
    }
  }

  static clearData(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch {
      // Silently ignore localStorage errors
    }
  }

  private static getDefaultData(): FileImport[] {
    return [
      {
        id: 1,
        filename: 'alumni_data_2024.csv',
        file_type: 'csv',
        upload_date: '2024-01-15T10:30:00Z',
        uploaded_by: '<EMAIL>',
        status: 'completed',
        records_count: 1250
      },
      {
        id: 2,
        filename: 'graduates_2023.xlsx',
        file_type: 'xlsx',
        upload_date: '2024-02-20T14:15:00Z',
        uploaded_by: '<EMAIL>',
        status: 'processing',
        records_count: 890
      },
      {
        id: 3,
        filename: 'contact_list.json',
        file_type: 'json',
        upload_date: '2024-03-10T09:45:00Z',
        uploaded_by: '<EMAIL>',
        status: 'completed',
        records_count: 567
      }
    ];
  }
}

class MockAPIService {
  static getFileImports(options: {
    page?: number;
    pageSize?: number;
    search?: string;
  } = {}): PaginatedResponse<FileImport> {
    const { page = 0, pageSize = 10, search } = options;
    let data = LocalStorageService.loadData();

    // Apply search filter
    if (search) {
      const searchLower = search.toLowerCase();
      data = data.filter(item =>
        item.filename.toLowerCase().includes(searchLower) ||
        item.file_type.toLowerCase().includes(searchLower) ||
        item.uploaded_by.toLowerCase().includes(searchLower)
      );
    }

    const total = data.length;
    const startIndex = page * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedData = data.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      total
    };
  }

  static updateFileImport(id: number, updates: Partial<FileImport>): FileImport | null {
    const data = LocalStorageService.loadData();
    const index = data.findIndex(item => item.id === id);

    if (index === -1) {
      return null;
    }

    const updatedItem = { ...data[index], ...updates };
    data[index] = updatedItem;
    LocalStorageService.saveData(data);

    return updatedItem;
  }

  static exportData(format: 'csv' | 'json'): string | FileImport[] {
    const data = LocalStorageService.loadData();

    if (format === 'json') {
      return data;
    }

    if (format === 'csv') {
      const headers = ['ID', 'Filename', 'File Type', 'Upload Date', 'Uploaded By', 'Status', 'Records Count'];
      const csvRows = [
        headers.join(','),
        ...data.map(item => [
          item.id,
          `"${item.filename}"`,
          item.file_type,
          item.upload_date,
          `"${item.uploaded_by}"`,
          item.status,
          item.records_count
        ].join(','))
      ];
      return csvRows.join('\n');
    }

    throw new Error(`Unsupported export format: ${format}`);
  }
}

export { MockAPIService, LocalStorageService };
export type { FileImport, PaginatedResponse };