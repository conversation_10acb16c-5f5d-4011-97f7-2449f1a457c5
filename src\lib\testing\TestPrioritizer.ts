import {
  PrioritizedTests,
  TestCase,
  RiskAssessment,
  TestRelevance,
  CodeChange,
  RiskAnalyzer,
  ChangeAnalyzer
} from './types';

export class TestPrioritizer {
  private riskAnalyzer: RiskAnalyzer;
  private changeAnalyzer: ChangeAnalyzer;
  private maxExecutionTime: number;

  constructor(
    riskAnalyzer: RiskAnalyzer,
    changeAnalyzer: ChangeAnalyzer,
    maxExecutionTime: number = 300000 // 5 minutes
  ) {
    this.riskAnalyzer = riskAnalyzer;
    this.changeAnalyzer = changeAnalyzer;
    this.maxExecutionTime = maxExecutionTime;
  }

  public async prioritizeTests(
    allTests: TestCase[],
    changes: CodeChange[]
  ): Promise<PrioritizedTests> {
    // Analyze risk of changes
    const riskAssessment = await this.riskAnalyzer.assessRisk(changes);

    // Calculate test relevance to changes
    const testRelevance = await this.calculateTestRelevance(allTests, changes);

    // Apply prioritization algorithm
    const prioritized = this.applyPrioritizationAlgorithm(
      allTests,
      riskAssessment,
      testRelevance
    );

    return {
      highPriority: prioritized.filter(t => t.priority === 'high').map(t => t.test),
      mediumPriority: prioritized.filter(t => t.priority === 'medium').map(t => t.test),
      lowPriority: prioritized.filter(t => t.priority === 'low').map(t => t.test),
      executionOrder: prioritized.map(t => t.test)
    };
  }

  private async calculateTestRelevance(
    tests: TestCase[],
    changes: CodeChange[]
  ): Promise<TestRelevance[]> {
    const relevancePromises = tests.map(async test => ({
      test,
      relevance: await this.changeAnalyzer.calculateRelevance(test, changes),
      dependencies: await this.changeAnalyzer.analyzeDependencies(test)
    }));

    return Promise.all(relevancePromises);
  }

  private applyPrioritizationAlgorithm(
    tests: TestCase[],
    riskAssessment: RiskAssessment,
    testRelevance: TestRelevance[]
  ): Array<{ test: TestCase; priority: 'high' | 'medium' | 'low'; score: number }> {
    const prioritized: Array<{ test: TestCase; priority: 'high' | 'medium' | 'low'; score: number }> = [];

    for (const test of tests) {
      const relevance = testRelevance.find(r => r.test.id === test.id);
      const score = this.calculatePriorityScore(test, relevance, riskAssessment);

      let priority: 'high' | 'medium' | 'low';
      if (score >= 0.8) {
        priority = 'high';
      } else if (score >= 0.5) {
        priority = 'medium';
      } else {
        priority = 'low';
      }

      prioritized.push({ test, priority, score });
    }

    // Sort by score descending
    prioritized.sort((a, b) => b.score - a.score);

    return prioritized;
  }

  private calculatePriorityScore(
    test: TestCase,
    relevance?: TestRelevance,
    riskAssessment?: RiskAssessment
  ): number {
    let score = this.calculateBaseScore(test);

    score += this.calculateRelevanceScore(relevance);
    score += this.calculateRiskScore(riskAssessment);
    score += this.calculateRecencyScore(test);
    score += this.calculateSpeedScore(test);

    return Math.min(score, 1.0);
  }

  private calculateBaseScore(test: TestCase): number {
    switch (test.priority) {
      case 'high': return 0.3;
      case 'medium': return 0.2;
      case 'low': return 0.1;
      default: return 0;
    }
  }

  private calculateRelevanceScore(relevance?: TestRelevance): number {
    return relevance ? relevance.relevance * 0.4 : 0;
  }

  private calculateRiskScore(riskAssessment?: RiskAssessment): number {
    if (!riskAssessment) return 0;

    let score = 0;
    switch (riskAssessment.overall) {
      case 'critical': score += 0.3; break;
      case 'high': score += 0.2; break;
      case 'medium': score += 0.1; break;
    }

    riskAssessment.factors.forEach(factor => {
      score += (factor.severity / 100) * 0.1;
    });

    return score;
  }

  private calculateRecencyScore(test: TestCase): number {
    if (!test.lastRun || !test.duration) return 0;

    const timeSinceLastRun = Date.now() - test.lastRun.getTime();
    return timeSinceLastRun < 24 * 60 * 60 * 1000 ? 0.1 : 0; // Within last 24 hours
  }

  private calculateSpeedScore(test: TestCase): number {
    return test.duration && test.duration < 1000 ? 0.05 : 0; // Fast tests (< 1 second)
  }

  public async selectTestsForExecution(
    allTests: TestCase[],
    changes: CodeChange[],
    timeBudget: number = this.maxExecutionTime
  ): Promise<TestCase[]> {
    // Get the prioritized array by re-running the prioritization algorithm
    const riskAssessment = await this.riskAnalyzer.assessRisk(changes);
    const testRelevance = await this.calculateTestRelevance(allTests, changes);
    const prioritized = this.applyPrioritizationAlgorithm(
      allTests,
      riskAssessment,
      testRelevance
    );

    let totalTime = 0;
    const selectedTests: TestCase[] = [];

    // Select tests within time budget, prioritizing high-priority tests
    for (const { test, priority } of prioritized) {
      const estimatedTime = test.duration || 1000; // Default 1 second if unknown

      if (totalTime + estimatedTime <= timeBudget) {
        selectedTests.push(test);
        totalTime += estimatedTime;
      } else if (priority === 'high') {
        // Always include high-priority tests, even if over budget
        selectedTests.push(test);
        totalTime += estimatedTime;
      }
    }

    return selectedTests;
  }

  public async getExecutionPlan(
    allTests: TestCase[],
    changes: CodeChange[]
  ): Promise<{
    plan: TestCase[];
    estimatedTime: number;
    coverage: number;
    riskReduction: number;
  }> {
    const selectedTests = await this.selectTestsForExecution(allTests, changes);
    const estimatedTime = selectedTests.reduce((sum, test) => sum + (test.duration || 1000), 0);

    // Calculate coverage (percentage of all tests selected)
    const coverage = (selectedTests.length / allTests.length) * 100;

    // Calculate risk reduction based on test priorities
    const highPrioritySelected = selectedTests.filter(t => t.priority === 'high').length;
    const totalHighPriority = allTests.filter(t => t.priority === 'high').length;
    const riskReduction = totalHighPriority > 0 ? (highPrioritySelected / totalHighPriority) * 100 : 100;

    return {
      plan: selectedTests,
      estimatedTime,
      coverage,
      riskReduction
    };
  }

  public setMaxExecutionTime(time: number): void {
    this.maxExecutionTime = time;
  }

  public getMaxExecutionTime(): number {
    return this.maxExecutionTime;
  }

  // Utility method to create a test case from a test function
  public static createTestCase(
    id: string,
    name: string,
    file: string,
    category: string = 'unit',
    priority: 'high' | 'medium' | 'low' = 'medium'
  ): TestCase {
    return {
      id,
      name,
      file,
      category,
      priority
    };
  }

  // Method to update test execution results
  public updateTestResults(_testId: string, _passed: boolean, _duration: number): void {
    // This would typically update a test database or cache
    // For now, we'll store the results internally (no-op)
    // Future: Implement proper result storage and reporting
  }
}

// Basic implementations of the engine interfaces
export class BasicRiskAnalyzer implements RiskAnalyzer {
  public async assessRisk(changes: CodeChange[]): Promise<RiskAssessment> {
    let overallRisk: 'low' | 'medium' | 'high' | 'critical' = 'low';
    const factors: RiskAssessment['factors'] = [];

    // Analyze each change
    for (const change of changes) {
      const risk = this.assessChangeRisk(change);
      factors.push({
        type: change.type,
        severity: risk.severity,
        description: risk.description
      });

      // Update overall risk
      if (risk.severity > 70 && overallRisk !== 'critical') {
        overallRisk = 'critical';
      } else if (risk.severity > 50 && overallRisk === 'low') {
        overallRisk = 'high';
      } else if (risk.severity > 30 && overallRisk === 'low') {
        overallRisk = 'medium';
      }
    }

    return {
      overall: overallRisk,
      factors
    };
  }

  private assessChangeRisk(change: CodeChange): { severity: number; description: string } {
    let severity = 0;
    let description = '';

    // Risk based on change type
    switch (change.type) {
      case 'added':
        severity += 30;
        description = `New code added: ${change.content.length} characters`;
        break;
      case 'modified':
        severity += 50;
        description = `Code modified: ${change.lines.length} lines changed`;
        break;
      case 'deleted':
        severity += 70;
        description = `Code deleted: ${change.lines.length} lines removed`;
        break;
    }

    // Risk based on file type
    if (change.file.includes('test')) {
      severity -= 20; // Tests are less risky
      description += ' (test file)';
    } else if (change.file.includes('config')) {
      severity += 20; // Config changes are riskier
      description += ' (config file)';
    }

    // Risk based on content patterns
    if (change.content.includes('password') || change.content.includes('secret')) {
      severity += 40;
      description += ' (security-related)';
    }

    if (change.content.includes('database') || change.content.includes('api')) {
      severity += 30;
      description += ' (infrastructure-related)';
    }

    return { severity: Math.min(severity, 100), description };
  }
}

export class BasicChangeAnalyzer implements ChangeAnalyzer {
  public async calculateRelevance(test: TestCase, changes: CodeChange[]): Promise<number> {
    let relevance = 0;

    for (const change of changes) {
      // Check if test file is related to changed file
      if (this.isTestRelatedToFile(test.file, change.file)) {
        relevance += 0.5;
      }

      // Check if test name contains keywords from change
      if (this.testMatchesChangeKeywords(test.name, change.content)) {
        relevance += 0.3;
      }

      // Check if test is in same directory or related module
      if (this.isSameModule(test.file, change.file)) {
        relevance += 0.2;
      }
    }

    return Math.min(relevance, 1.0);
  }

  public async analyzeDependencies(test: TestCase): Promise<string[]> {
    // This would analyze the test file to find its dependencies
    // For now, return basic dependencies
    const dependencies: string[] = [];

    if (test.file.includes('component')) {
      dependencies.push('react', 'react-dom');
    }

    if (test.file.includes('api')) {
      dependencies.push('axios', 'api-service');
    }

    return dependencies;
  }

  private isTestRelatedToFile(testFile: string, changedFile: string): boolean {
    // Check if test file corresponds to the changed file
    const testBaseName = testFile.replace('.test.', '.').replace('.spec.', '.');
    const changedBaseName = changedFile.replace(/\..+$/, '');

    return testBaseName.includes(changedBaseName) || changedBaseName.includes(testBaseName.replace('.test', ''));
  }

  private testMatchesChangeKeywords(testName: string, content: string): boolean {
    const keywords = this.extractKeywords(content);
    const testWords = testName.toLowerCase().split(/[\s\-_]+/);

    return keywords.some(keyword =>
      testWords.some(word => word.includes(keyword) || keyword.includes(word))
    );
  }

  private extractKeywords(content: string): string[] {
    // Extract meaningful keywords from code changes
    const keywords: string[] = [];
    const lines = content.split('\n');

    for (const line of lines) {
      // Look for function names, variable names, etc.
      const matches = line.match(/\b[a-zA-Z_$][a-zA-Z0-9_$]*\b/g);
      if (matches) {
        keywords.push(...matches.filter(word => word.length > 2));
      }
    }

    return [...new Set(keywords)].map(k => k.toLowerCase());
  }

  private isSameModule(testFile: string, changedFile: string): boolean {
    // Check if files are in the same directory or module
    const testDir = testFile.split('/').slice(0, -1).join('/');
    const changedDir = changedFile.split('/').slice(0, -1).join('/');

    return testDir === changedDir || testDir.includes(changedDir) || changedDir.includes(testDir);
  }
}