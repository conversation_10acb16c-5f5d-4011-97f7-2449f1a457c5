import {
  PerformanceTestResults,
  PerformanceMetrics,
  PerformanceRegression,
  BenchmarkEngine,
  MetricsCollector
} from './types';
import { readFileSync, writeFileSync, existsSync } from 'fs';

export class PerformanceTester {
  private benchmarkEngine: BenchmarkEngine;
  private metricsCollector: MetricsCollector;
  private baselineFile: string;
  private threshold: number;

  constructor(
    benchmarkEngine: BenchmarkEngine,
    metricsCollector: MetricsCollector,
    baselineFile = 'src/test/performance-baseline.json',
    threshold = 0.05 // 5% degradation threshold
  ) {
    this.benchmarkEngine = benchmarkEngine;
    this.metricsCollector = metricsCollector;
    this.baselineFile = baselineFile;
    this.threshold = threshold;
  }

  public async runPerformanceRegressionTests(
    testFunctions?: Array<{ name: string; fn: () => void | Promise<void>; iterations?: number }>
  ): Promise<PerformanceTestResults> {
    const functions = testFunctions || await this.getDefaultTestFunctions();

    // Load baseline metrics
    const baseline = await this.getBaselineMetrics();

    // Measure current performance
    const current = await this.measureCurrentPerformance(functions);

    // Detect regressions
    const regressions = this.detectRegressions(baseline, current);

    // Detect improvements
    const improvements = this.detectImprovements(baseline, current);

    return {
      baseline,
      current,
      regressions,
      improvements,
      overallTrend: this.calculateOverallTrend(regressions, improvements),
      recommendations: await this.generateRecommendations(regressions)
    };
  }

  private async getDefaultTestFunctions(): Promise<Array<{ name: string; fn: () => void | Promise<void>; iterations?: number }>> {
    // Default performance test functions
    return [
      {
        name: 'render-time',
        fn: async () => {
          // This would typically render a component and measure time
          await new Promise(resolve => setTimeout(resolve, 10));
        },
        iterations: 100
      },
      {
        name: 'data-processing',
        fn: () => {
          // Simulate data processing
          const data = Array.from({ length: 1000 }, (_, i) => ({ id: i, value: Math.random() }));
          data.sort((a, b) => a.value - b.value);
        },
        iterations: 50
      },
      {
        name: 'memory-usage',
        fn: () => {
          // Simulate memory allocation
          for (let i = 0; i < 100; i++) {
            new Array(1000).fill(Math.random());
          }
          // Let GC clean up
        },
        iterations: 10
      }
    ];
  }

  private async getBaselineMetrics(): Promise<PerformanceMetrics> {
    if (existsSync(this.baselineFile)) {
      const data = readFileSync(this.baselineFile, 'utf-8');
      return JSON.parse(data);
    }

    // Return empty baseline if file doesn't exist
    return {};
  }

  private async measureCurrentPerformance(
    functions: Array<{ name: string; fn: () => void | Promise<void>; iterations?: number }>
  ): Promise<PerformanceMetrics> {
    const metrics: PerformanceMetrics = {};

    for (const testFunction of functions) {
      const iterations = testFunction.iterations || 100;
      const result = await this.benchmarkEngine.measurePerformance(testFunction.fn, iterations);

      // Store results with function name prefix
      Object.entries(result).forEach(([key, value]) => {
        metrics[`${testFunction.name}-${key}`] = value;
      });
    }

    // Collect additional system metrics
    const systemMetrics = await this.metricsCollector.collect();
    Object.assign(metrics, systemMetrics);

    return metrics;
  }

  private detectRegressions(
    baseline: PerformanceMetrics,
    current: PerformanceMetrics
  ): PerformanceRegression[] {
    const regressions: PerformanceRegression[] = [];

    Object.keys(current).forEach(metric => {
      const baselineValue = baseline[metric];
      const currentValue = current[metric];

      if (baselineValue !== undefined && currentValue !== undefined) {
        const change = (currentValue - baselineValue) / baselineValue;

        if (change > this.threshold) {
          regressions.push({
            metric,
            baselineValue,
            currentValue,
            changePercentage: change * 100,
            severity: this.calculateSeverity(change)
          });
        }
      }
    });

    return regressions;
  }

  private detectImprovements(
    baseline: PerformanceMetrics,
    current: PerformanceMetrics
  ): PerformanceRegression[] {
    const improvements: PerformanceRegression[] = [];

    Object.keys(current).forEach(metric => {
      const baselineValue = baseline[metric];
      const currentValue = current[metric];

      if (baselineValue !== undefined && currentValue !== undefined) {
        const change = (baselineValue - currentValue) / baselineValue;

        if (change > this.threshold) {
          improvements.push({
            metric,
            baselineValue,
            currentValue,
            changePercentage: -(change * 100), // Negative for improvement
            severity: this.calculateSeverity(change)
          });
        }
      }
    });

    return improvements;
  }

  private calculateSeverity(change: number): 'low' | 'medium' | 'high' | 'critical' {
    const absChange = Math.abs(change);

    if (absChange > 0.5) return 'critical'; // >50% change
    if (absChange > 0.25) return 'high'; // >25% change
    if (absChange > 0.1) return 'medium'; // >10% change
    return 'low'; // <=10% change
  }

  private calculateOverallTrend(
    regressions: PerformanceRegression[],
    improvements: PerformanceRegression[]
  ): 'improving' | 'stable' | 'degrading' {
    const regressionScore = regressions.reduce((sum, r) => sum + Math.abs(r.changePercentage), 0);
    const improvementScore = improvements.reduce((sum, i) => sum + Math.abs(i.changePercentage), 0);

    if (regressionScore > improvementScore * 1.5) return 'degrading';
    if (improvementScore > regressionScore * 1.5) return 'improving';
    return 'stable';
  }

  private async generateRecommendations(regressions: PerformanceRegression[]): Promise<string[]> {
    const recommendations: string[] = [];

    for (const regression of regressions) {
      const metric = regression.metric.toLowerCase();

      if (metric.includes('render') || metric.includes('paint')) {
        recommendations.push(`Optimize rendering performance for ${regression.metric}. Consider memoization, virtualization, or reducing re-renders.`);
      } else if (metric.includes('memory')) {
        recommendations.push(`Address memory usage regression in ${regression.metric}. Check for memory leaks or optimize data structures.`);
      } else if (metric.includes('cpu') || metric.includes('time')) {
        recommendations.push(`Improve execution time for ${regression.metric}. Consider algorithm optimization or caching strategies.`);
      } else if (metric.includes('bundle') || metric.includes('size')) {
        recommendations.push(`Reduce bundle size affecting ${regression.metric}. Consider code splitting or tree shaking.`);
      } else {
        recommendations.push(`Investigate performance regression in ${regression.metric} (${regression.changePercentage.toFixed(2)}% degradation).`);
      }
    }

    if (regressions.length === 0) {
      recommendations.push('Performance is stable. Continue monitoring for any future regressions.');
    }

    return recommendations;
  }

  public async updateBaseline(currentMetrics?: PerformanceMetrics): Promise<void> {
    const metrics = currentMetrics || await this.metricsCollector.collect();
    writeFileSync(this.baselineFile, JSON.stringify(metrics, null, 2));
  }

  public setThreshold(threshold: number): void {
    this.threshold = threshold;
  }

  public getThreshold(): number {
    return this.threshold;
  }

  public async compareMetrics(
    metrics1: PerformanceMetrics,
    metrics2: PerformanceMetrics
  ): Promise<{ regressions: PerformanceRegression[]; improvements: PerformanceRegression[] }> {
    return {
      regressions: this.detectRegressions(metrics1, metrics2),
      improvements: this.detectImprovements(metrics1, metrics2)
    };
  }
}

// Basic implementations of the engine interfaces
export class BasicBenchmarkEngine implements BenchmarkEngine {
  public async measurePerformance(
    fn: () => void | Promise<void>,
    iterations: number = 100
  ): Promise<PerformanceMetrics> {
    const startTime = performance.now();
    const startMemory = (performance as { memory?: { usedJSHeapSize: number } }).memory?.usedJSHeapSize || 0;

    // Run the function multiple times
    for (let i = 0; i < iterations; i++) {
      await fn();
    }

    const endTime = performance.now();
    const endMemory = (performance as { memory?: { usedJSHeapSize: number } }).memory?.usedJSHeapSize || 0;

    const totalTime = endTime - startTime;
    const averageTime = totalTime / iterations;
    const memoryDelta = endMemory - startMemory;

    return {
      totalTime,
      averageTime,
      iterations,
      memoryDelta,
      memoryPerIteration: memoryDelta / iterations,
      operationsPerSecond: iterations / (totalTime / 1000)
    };
  }
}

export class BasicMetricsCollector implements MetricsCollector {
  public async collect(): Promise<PerformanceMetrics> {
    const metrics: PerformanceMetrics = {};

    // Browser performance metrics
    if (typeof performance !== 'undefined') {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart;
        metrics.loadComplete = navigation.loadEventEnd - navigation.loadEventStart;
      }

      // Memory info (Chrome only)
      const memory = (performance as { memory?: { usedJSHeapSize: number; totalJSHeapSize: number; jsHeapSizeLimit: number } }).memory;
      if (memory) {
        metrics.heapUsed = memory.usedJSHeapSize;
        metrics.heapTotal = memory.totalJSHeapSize;
        metrics.heapLimit = memory.jsHeapSizeLimit;
      }
    }

    // Additional timing metrics
    metrics.timestamp = Date.now();

    return metrics;
  }
}