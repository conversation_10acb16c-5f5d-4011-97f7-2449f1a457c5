import { createContext } from 'react';
import { ThemeContextType } from './types';

// Production-safe logger utility
export const logger = {
  info: (message: string, ...args: unknown[]) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.log(`[ThemeProvider] ${message}`, ...args);
    }
  },
  warn: (message: string, ...args: unknown[]) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.warn(`[ThemeProvider] ${message}`, ...args);
    }
  },
  error: (message: string, ...args: unknown[]) => {
    // eslint-disable-next-line no-console
    console.error(`[ThemeProvider] ${message}`, ...args);
  }
};

// Create theme context
export const ThemeContext = createContext<ThemeContextType | null>(null);