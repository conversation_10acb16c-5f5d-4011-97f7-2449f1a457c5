import { AWSDataService, getAWSConfigStatus } from './awsData';

// Production-safe logger utility
const logger = {
  info: (message: string, ...args: unknown[]) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.log(`[AWSTestUtility] ${message}`, ...args);
    }
  },
  warn: (message: string, ...args: unknown[]) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.warn(`[AWSTestUtility] ${message}`, ...args);
    }
  },
  error: (message: string, ...args: unknown[]) => {
    // eslint-disable-next-line no-console
    console.error(`[AWSTestUtility] ${message}`, ...args);
  }
};

/**
 * AWS Integration Test Utility
 * Use this to verify AWS configuration and connectivity
 */

const ERROR_MESSAGES = {
  UNKNOWN_ERROR: 'Unknown error',
  CONFIG_TEST_FAILED: 'Configuration test failed',
  DYNAMODB_CONNECTION_FAILED: 'DynamoDB connection failed',
  WRITE_OPERATIONS_FAILED: 'Write operations failed'
} as const;

export interface AWSTestResult {
  success: boolean;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

export class AWSTestUtility {
  /**
   * Test AWS configuration
   */
  static async testConfiguration(): Promise<AWSTestResult> {
    try {
      const config = getAWSConfigStatus();

      if (!config.isConfigured) {
        return {
          success: false,
          message: 'AWS configuration incomplete',
          details: {
            hasAccessKey: config.hasAccessKey,
            hasSecretKey: config.hasSecretKey,
            hasRegion: config.hasRegion,
            tableName: config.tableName
          },
          timestamp: new Date().toISOString()
        };
      }

      return {
        success: true,
        message: 'AWS configuration is valid',
        details: config,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        message: `${ERROR_MESSAGES.CONFIG_TEST_FAILED}: ${error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Test DynamoDB connectivity
   */
  static async testConnectivity(): Promise<AWSTestResult> {
    try {
      // Try to fetch data (will fail gracefully if no data exists)
      const result = await AWSDataService.getFileImports({
        page: 0,
        pageSize: 1
      });

      return {
        success: true,
        message: 'Successfully connected to DynamoDB',
        details: {
          totalRecords: result.total,
          canRead: true,
          canWrite: true // We'll test this next
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        message: `${ERROR_MESSAGES.DYNAMODB_CONNECTION_FAILED}: ${error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR}`,
        details: { error },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Test write operations
   */
  static async testWriteOperations(): Promise<AWSTestResult> {
    try {
      // Create a test record
      const testRecord = {
        filename: 'aws_test_file.csv',
        file_type: 'CSV',
        upload_date: new Date().toISOString(),
        status: 'completed' as const,
        records_count: 50,
        processed_records: 50,
        errors_count: 0,
        uploaded_by: 'aws_test',
        file_size: '512KB'
      };

      const created = await AWSDataService.createFileImport(testRecord);

      // Clean up - delete the test record
      if (created?.id) {
        await AWSDataService.deleteFileImport(created.id);
      }

      return {
        success: true,
        message: 'Write operations working correctly',
        details: {
          createdRecord: created?.id,
          cleanupSuccessful: true
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        message: `${ERROR_MESSAGES.WRITE_OPERATIONS_FAILED}: ${error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR}`,
        details: { error },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
    * Run all tests
    */
   static async runAllTests(): Promise<{
     configuration: AWSTestResult;
     connectivity: AWSTestResult;
     writeOperations: AWSTestResult;
     overall: {
       success: boolean;
       message: string;
       timestamp: string;
     };
   }> {
     logger.info('Running AWS Integration Tests...');

     const configuration = await this.testConfiguration();
     logger.info(`Configuration Test: ${configuration.success ? 'PASSED' : 'FAILED'} - ${configuration.message}`);

     const connectivity = configuration.success ? await this.testConnectivity() : {
       success: false,
       message: 'Skipped - configuration failed',
       timestamp: new Date().toISOString()
     };
     logger.info(`Connectivity Test: ${connectivity.success ? 'PASSED' : 'FAILED'} - ${connectivity.message}`);

     const writeOperations = connectivity.success ? await this.testWriteOperations() : {
       success: false,
       message: 'Skipped - connectivity failed',
       timestamp: new Date().toISOString()
     };
     logger.info(`Write Operations Test: ${writeOperations.success ? 'PASSED' : 'FAILED'} - ${writeOperations.message}`);

     const overall = {
       success: configuration.success && connectivity.success && writeOperations.success,
       message: this.getOverallMessage(configuration, connectivity, writeOperations),
       timestamp: new Date().toISOString()
     };

     logger.info(`Overall Result: ${overall.success ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'} - ${overall.message}`);
     return {
       configuration,
       connectivity,
       writeOperations,
       overall
     };
   }

  private static getOverallMessage(config: AWSTestResult, connect: AWSTestResult, write: AWSTestResult): string {
    if (config.success && connect.success && write.success) {
      return '✅ All AWS integration tests passed!';
    }

    const failures = [];
    if (!config.success) failures.push('configuration');
    if (!connect.success) failures.push('connectivity');
    if (!write.success) failures.push('write operations');

    return `❌ Failed tests: ${failures.join(', ')}`;
  }
}

// Browser console utility for easy testing
if (typeof window !== 'undefined') {
  // Make it available in browser console
  (window as unknown as Record<string, unknown>).awsTest = AWSTestUtility;

  if (import.meta.env.DEV) {
    // eslint-disable-next-line no-console
    console.log(`
🔧 AWS Test Utility Available!
Run these commands in the browser console:

// Test configuration
awsTest.testConfiguration().then(console.log)

// Test connectivity
awsTest.testConnectivity().then(console.log)

// Test write operations
awsTest.testWriteOperations().then(console.log)

// Run all tests
awsTest.runAllTests().then(console.log)
    `);
  }
}