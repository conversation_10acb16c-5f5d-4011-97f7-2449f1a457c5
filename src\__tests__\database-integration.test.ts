import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { MySQLDataService } from '@/lib/mysqlData'
import { MySQLTestUtility } from '@/lib/mysqlTest'

/**
 * Integration tests for AWS MySQL database connectivity
 * These tests run against the actual database and require proper environment configuration
 * 
 * To run these tests:
 * 1. Ensure your .env file has correct AWS RDS MySQL credentials
 * 2. Run: npm run test -- --run database-integration.test.ts
 * 
 * Note: These tests are marked as integration tests and may be skipped in CI/CD
 */

const isIntegrationTestEnabled = () => {
  // For this demo, we'll always enable integration tests
  // In production, you might want to check environment variables
  return true
}

const skipIfNotIntegration = () => {
  if (!isIntegrationTestEnabled()) {
    console.log('⏭️  Skipping integration tests. Set VITEST_INTEGRATION_TESTS=true to enable.')
    return true
  }
  return false
}

describe('Database Integration Tests', () => {
  beforeAll(async () => {
    if (skipIfNotIntegration()) return
    
    console.log('🔧 Setting up database integration tests...')
    console.log('📍 Database Host:', import.meta.env.VITE_DB_HOST)
    console.log('👤 Database User:', import.meta.env.VITE_DB_USER)
    console.log('🗄️  Database Name:', import.meta.env.VITE_DB_NAME)
  })

  afterAll(async () => {
    if (skipIfNotIntegration()) return
    
    console.log('🧹 Cleaning up database connections...')
    const { closeConnectionPool } = await import('@/lib/mysqlData')
    await closeConnectionPool()
  })

  describe('Real Database Connectivity', () => {
    it('should connect to AWS RDS MySQL database', async () => {
      if (skipIfNotIntegration()) return
      
      console.log('🔍 Testing real database connection...')
      
      const result = await MySQLTestUtility.testConnectivity()
      
      expect(result.success).toBe(true)
      expect(result.message).toBe('Successfully connected to MySQL database')
      expect(result.details?.connectionSuccessful).toBe(true)
      expect(result.timestamp).toBeDefined()
      
      console.log('✅ Database connection successful!')
    }, 30000) // 30 second timeout for network operations

    it('should verify database configuration', async () => {
      if (skipIfNotIntegration()) return
      
      const { checkMySQLConfiguration, getMySQLConfigStatus } = await import('@/lib/mysqlData')
      
      expect(checkMySQLConfiguration()).toBe(true)
      
      const status = getMySQLConfigStatus()
      expect(status.isConfigured).toBe(true)
      expect(status.hasHost).toBe(true)
      expect(status.hasUser).toBe(true)
      expect(status.hasPassword).toBe(true)
      expect(status.hasDatabase).toBe(true)
      
      console.log('✅ Database configuration is valid')
    })

    it('should execute basic SQL queries', async () => {
      if (skipIfNotIntegration()) return
      
      console.log('🔍 Testing basic SQL query execution...')
      
      const isConnected = await MySQLDataService.testConnection()
      expect(isConnected).toBe(true)
      
      console.log('✅ Basic SQL queries working!')
    }, 15000)
  })

  describe('Database Schema Validation', () => {
    it('should verify file_imports table exists', async () => {
      if (skipIfNotIntegration()) return
      
      console.log('🔍 Checking if file_imports table exists...')
      
      try {
        // Try to fetch from file_imports table (will fail if table doesn't exist)
        await MySQLDataService.getFileImports({ page: 1, limit: 1 })
        console.log('✅ file_imports table exists and is accessible')
      } catch (error) {
        console.log('⚠️  file_imports table may not exist or is not accessible')
        console.log('Error:', error instanceof Error ? error.message : 'Unknown error')

        // This is expected if the table doesn't exist yet
        expect(error instanceof Error).toBe(true)
        expect(error.message).toContain('file_imports')
      }
    }, 15000)

    it('should handle missing tables gracefully', async () => {
      if (skipIfNotIntegration()) return
      
      console.log('🔍 Testing graceful handling of missing tables...')
      
      try {
        await MySQLDataService.getFileImports({ page: 1, limit: 10 })
      } catch (error) {
        // Should throw a descriptive error
        expect(error instanceof Error).toBe(true)
        expect(error.message).toContain('Failed to fetch file imports')
        console.log('✅ Missing table handled gracefully with descriptive error')
      }
    }, 15000)
  })

  describe('Database Performance', () => {
    it('should connect within reasonable time', async () => {
      if (skipIfNotIntegration()) return
      
      console.log('⏱️  Testing connection performance...')
      
      const startTime = Date.now()
      const result = await MySQLTestUtility.testConnectivity()
      const endTime = Date.now()

      const connectionTime = endTime - startTime

      expect(result.success).toBe(true)
      expect(connectionTime).toBeLessThan(10000) // Should connect within 10 seconds

      console.log(`✅ Connection established in ${connectionTime}ms`)
    }, 15000)

    it('should handle concurrent connections', async () => {
      if (skipIfNotIntegration()) return

      console.log('🔄 Testing concurrent connections...')

      const promises = Array.from({ length: 5 }, () =>
        MySQLDataService.testConnection()
      )

      const results = await Promise.all(promises)

      expect(results.every(result => result === true)).toBe(true)
      console.log('✅ Concurrent connections handled successfully')
    }, 20000)
  })

  describe('Error Handling', () => {
    it('should handle network timeouts gracefully', async () => {
      if (skipIfNotIntegration()) return

      console.log('🔍 Testing timeout handling...')

      // This test verifies that our timeout configuration works
      // The actual timeout is handled by the mysql2 library configuration
      const result = await MySQLTestUtility.testConnectivity()
      
      // Should either succeed or fail gracefully (not hang indefinitely)
      expect(typeof result.success).toBe('boolean')
      expect(result.timestamp).toBeDefined()
      
      console.log('✅ Timeout handling working correctly')
    }, 65000) // Slightly longer than our 60s timeout
  })
})

// Helper function to run integration tests manually
export const runDatabaseIntegrationTests = async () => {
  console.log('🚀 Running database integration tests manually...')
  
  try {
    // Test basic connectivity
    console.log('\n1. Testing basic connectivity...')
    const connectivityResult = await MySQLTestUtility.testConnectivity()
    console.log('Result:', connectivityResult)
    
    // Test configuration
    console.log('\n2. Testing configuration...')
    const { getMySQLConfigStatus } = await import('@/lib/mysqlData')
    const configStatus = getMySQLConfigStatus()
    console.log('Config Status:', configStatus)
    
    // Test basic operations
    console.log('\n3. Testing basic operations...')
    const isConnected = await MySQLDataService.testConnection()
    console.log('Connection Test:', isConnected)
    
    console.log('\n✅ All manual tests completed!')
    return true
  } catch (error) {
    console.error('\n❌ Manual tests failed:', error)
    return false
  }
}
