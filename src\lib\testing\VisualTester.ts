import {
  VisualTestResults,
  Screenshot,
  ImageComparison,
  ScreenshotEngine,
  ImageComparisonEngine
} from './types';
import { readFileSync, writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { createHash } from 'crypto';

export class VisualTester {
  private screenshotEngine: ScreenshotEngine;
  private comparisonEngine: ImageComparisonEngine;
  private baselineDir: string;
  private currentDir: string;
  private diffDir: string;

  constructor(
    screenshotEngine: ScreenshotEngine,
    comparisonEngine: ImageComparisonEngine,
    baselineDir = 'src/test/visual-baselines',
    currentDir = 'src/test/visual-current',
    diffDir = 'src/test/visual-diffs'
  ) {
    this.screenshotEngine = screenshotEngine;
    this.comparisonEngine = comparisonEngine;
    this.baselineDir = baselineDir;
    this.currentDir = currentDir;
    this.diffDir = diffDir;

    // Ensure directories exist
    this.ensureDirectoryExists(baselineDir);
    this.ensureDirectoryExists(currentDir);
    this.ensureDirectoryExists(diffDir);
  }

  public async runVisualRegressionTests(
    components?: Array<{ selector: string; name: string }>
  ): Promise<VisualTestResults> {
    const testComponents = components || await this.getTestableComponents();

    // Capture current screenshots
    const currentScreenshots = await this.captureCurrentScreenshots(testComponents);

    // Load or capture baseline screenshots
    const baselineScreenshots = await this.getBaselineScreenshots(testComponents);

    // Compare screenshots
    const comparisons = await this.compareScreenshots(baselineScreenshots, currentScreenshots);

    // Generate diff images for failures
    const failures = comparisons.filter(c => !c.passed);
    await this.generateDiffImages(failures);

    return {
      totalTests: comparisons.length,
      passed: comparisons.filter(c => c.passed).length,
      failed: failures.length,
      failures: failures.map(f => ({
        component: f.component,
        diffPercentage: f.diffPercentage,
        diffImage: f.diffImage
      }))
    };
  }

  private async getTestableComponents(): Promise<Array<{ selector: string; name: string }>> {
    // This would typically scan the codebase for components
    // For now, return a basic set
    return [
      { selector: '[data-testid="app"]', name: 'App' },
      { selector: '[data-testid="sidebar"]', name: 'Sidebar' },
      { selector: '[data-testid="header"]', name: 'Header' },
      { selector: 'button', name: 'Buttons' },
      { selector: 'input', name: 'Inputs' }
    ];
  }

  private async captureCurrentScreenshots(
    components: Array<{ selector: string; name: string }>
  ): Promise<Screenshot[]> {
    const screenshots: Screenshot[] = [];

    for (const component of components) {
      try {
        const screenshot = await this.screenshotEngine.capture(component.selector, component.name);
        screenshots.push(screenshot);

        // Save screenshot to current directory
        const fileName = `${component.name.toLowerCase().replace(/\s+/g, '-')}.png`;
        const filePath = join(this.currentDir, fileName);
        writeFileSync(filePath, screenshot.data);
      } catch {
        // Failed to capture screenshot - silently continue
      }
    }

    return screenshots;
  }

  private async getBaselineScreenshots(
    components: Array<{ selector: string; name: string }>
  ): Promise<Screenshot[]> {
    const screenshots: Screenshot[] = [];

    for (const component of components) {
      const fileName = `${component.name.toLowerCase().replace(/\s+/g, '-')}.png`;
      const filePath = join(this.baselineDir, fileName);

      if (existsSync(filePath)) {
        // Load existing baseline
        const data = readFileSync(filePath);
        screenshots.push({
          name: component.name,
          data,
          timestamp: new Date() // This should be stored with the baseline
        });
      } else {
        // Capture new baseline
        try {
          const screenshot = await this.screenshotEngine.capture(component.selector, component.name);
          writeFileSync(filePath, screenshot.data);
          screenshots.push(screenshot);
        } catch {
          // Failed to capture baseline - silently continue
        }
      }
    }

    return screenshots;
  }

  private async compareScreenshots(
    baselineScreenshots: Screenshot[],
    currentScreenshots: Screenshot[]
  ): Promise<ImageComparison[]> {
    const comparisons: ImageComparison[] = [];

    for (const current of currentScreenshots) {
      const baseline = baselineScreenshots.find(b => b.name === current.name);

      if (!baseline) {
        // No baseline found, consider it a failure
        comparisons.push({
          component: current.name,
          passed: false,
          diffPercentage: 100,
          diffImage: undefined
        });
        continue;
      }

      try {
        const comparison = await this.comparisonEngine.compare(baseline.data, current.data);
        comparisons.push({
          component: current.name,
          passed: comparison.passed,
          diffPercentage: comparison.diffPercentage,
          diffImage: comparison.diffImage
        });
      } catch {
        // Failed to compare screenshots - mark as failed
        comparisons.push({
          component: current.name,
          passed: false,
          diffPercentage: 100,
          diffImage: undefined
        });
      }
    }

    return comparisons;
  }

  private async generateDiffImages(failures: ImageComparison[]): Promise<void> {
    for (const failure of failures) {
      if (failure.diffImage) {
        const fileName = `${failure.component.toLowerCase().replace(/\s+/g, '-')}-diff.png`;
        const filePath = join(this.diffDir, fileName);
        writeFileSync(filePath, failure.diffImage);
      }
    }
  }

  public async updateBaselines(): Promise<void> {
    const components = await this.getTestableComponents();

    for (const component of components) {
      const currentFileName = `${component.name.toLowerCase().replace(/\s+/g, '-')}.png`;
      const currentPath = join(this.currentDir, currentFileName);
      const baselinePath = join(this.baselineDir, currentFileName);

      if (existsSync(currentPath)) {
        // Copy current to baseline
        const data = readFileSync(currentPath);
        writeFileSync(baselinePath, data);
      }
    }
  }

  public async approveDiff(componentName: string): Promise<void> {
    const currentFileName = `${componentName.toLowerCase().replace(/\s+/g, '-')}.png`;
    const currentPath = join(this.currentDir, currentFileName);
    const baselinePath = join(this.baselineDir, currentFileName);

    if (existsSync(currentPath)) {
      const data = readFileSync(currentPath);
      writeFileSync(baselinePath, data);
    }
  }

  private ensureDirectoryExists(dirPath: string): void {
    if (!existsSync(dirPath)) {
      mkdirSync(dirPath, { recursive: true });
    }
  }

  public getBaselinePath(componentName: string): string {
    const fileName = `${componentName.toLowerCase().replace(/\s+/g, '-')}.png`;
    return join(this.baselineDir, fileName);
  }

  public getCurrentPath(componentName: string): string {
    const fileName = `${componentName.toLowerCase().replace(/\s+/g, '-')}.png`;
    return join(this.currentDir, fileName);
  }

  public getDiffPath(componentName: string): string {
    const fileName = `${componentName.toLowerCase().replace(/\s+/g, '-')}-diff.png`;
    return join(this.diffDir, fileName);
  }
}

// Basic implementations of the engine interfaces
export class BasicScreenshotEngine implements ScreenshotEngine {
  public async capture(selector: string, name: string): Promise<Screenshot> {
    // This is a placeholder implementation
    // In a real scenario, this would use a headless browser like Puppeteer or Playwright
    // to capture actual screenshots

    // For now, return a mock screenshot
    const mockData = Buffer.from(`Mock screenshot data for ${name} at ${selector}`);

    return {
      name,
      data: mockData,
      timestamp: new Date()
    };
  }
}

export class BasicImageComparisonEngine implements ImageComparisonEngine {
  private threshold: number;

  constructor(threshold = 0.01) { // 1% difference threshold
    this.threshold = threshold;
  }

  public async compare(image1: Buffer, image2: Buffer): Promise<ImageComparison> {
    // This is a simplified comparison
    // In a real scenario, you'd use a library like pixelmatch or resemble.js

    const hash1 = createHash('md5').update(image1).digest('hex');
    const hash2 = createHash('md5').update(image2).digest('hex');

    const passed = hash1 === hash2;
    const diffPercentage = passed ? 0 : 100; // Simplified

    return {
      component: '', // Will be set by caller
      passed,
      diffPercentage,
      diffImage: passed ? undefined : image2 // Simplified diff
    };
  }
}