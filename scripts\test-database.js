#!/usr/bin/env node

/**
 * Database Connectivity Test Runner
 * 
 * This script provides comprehensive testing for AWS MySQL database connectivity
 * It can be run independently of the main test suite for debugging and validation
 * 
 * Usage:
 *   node scripts/test-database.js [options]
 * 
 * Options:
 *   --quick     Run only basic connectivity tests
 *   --full      Run comprehensive tests including performance
 *   --verbose   Show detailed output
 *   --help      Show this help message
 */

import mysql from 'mysql2/promise'
import dotenv from 'dotenv'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

// Load environment variables
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)
const rootDir = join(__dirname, '..')

dotenv.config({ path: join(rootDir, '.env') })
dotenv.config({ path: join(rootDir, '.env.local') })

// Configuration
const DB_CONFIG = {
  host: process.env.DB_HOST || process.env.VITE_DB_HOST,
  user: process.env.DB_USER || process.env.VITE_DB_USER,
  password: process.env.DB_PASSWORD || process.env.VITE_DB_PASSWORD,
  database: process.env.DB_NAME || process.env.VITE_DB_NAME,
  port: parseInt(process.env.DB_PORT || process.env.VITE_DB_PORT || '3306'),
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000,
}

// Parse command line arguments
const args = process.argv.slice(2)
const options = {
  quick: args.includes('--quick'),
  full: args.includes('--full'),
  verbose: args.includes('--verbose'),
  help: args.includes('--help')
}

// Helper functions
const log = (message, ...args) => {
  console.log(`[${new Date().toISOString()}] ${message}`, ...args)
}

const logVerbose = (message, ...args) => {
  if (options.verbose) {
    console.log(`[VERBOSE] ${message}`, ...args)
  }
}

const logError = (message, ...args) => {
  console.error(`[ERROR] ${message}`, ...args)
}

const logSuccess = (message, ...args) => {
  console.log(`✅ ${message}`, ...args)
}

const logWarning = (message, ...args) => {
  console.warn(`⚠️  ${message}`, ...args)
}

// Test functions
async function testConfiguration() {
  log('🔧 Testing database configuration...')
  
  const requiredFields = ['host', 'user', 'password', 'database']
  const missingFields = requiredFields.filter(field => !DB_CONFIG[field])
  
  if (missingFields.length > 0) {
    logError(`Missing required configuration: ${missingFields.join(', ')}`)
    return false
  }
  
  logVerbose('Configuration:', {
    host: DB_CONFIG.host,
    user: DB_CONFIG.user,
    database: DB_CONFIG.database,
    port: DB_CONFIG.port,
    password: DB_CONFIG.password ? '[REDACTED]' : '[MISSING]'
  })
  
  logSuccess('Database configuration is valid')
  return true
}

async function testBasicConnectivity() {
  log('🔍 Testing basic database connectivity...')
  
  let connection = null
  
  try {
    const startTime = Date.now()
    connection = await mysql.createConnection(DB_CONFIG)
    const connectionTime = Date.now() - startTime
    
    logVerbose(`Connection established in ${connectionTime}ms`)
    
    // Test basic query
    const [rows] = await connection.execute('SELECT 1 as test')
    
    if (rows[0]?.test === 1) {
      logSuccess(`Basic connectivity test passed (${connectionTime}ms)`)
      return true
    } else {
      logError('Basic query returned unexpected result:', rows)
      return false
    }
  } catch (error) {
    logError('Basic connectivity test failed:', error.message)
    return false
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

async function testDatabaseSchema() {
  log('🗄️  Testing database schema...')
  
  let connection = null
  
  try {
    connection = await mysql.createConnection(DB_CONFIG)
    
    // List all tables
    const [tables] = await connection.execute('SHOW TABLES')
    logVerbose('Available tables:', tables.map(t => Object.values(t)[0]))
    
    // Check for expected tables
    const tableNames = tables.map(t => Object.values(t)[0])
    const expectedTables = ['file_imports']
    
    for (const expectedTable of expectedTables) {
      if (tableNames.includes(expectedTable)) {
        logSuccess(`Table '${expectedTable}' exists`)
        
        // Get table structure
        const [columns] = await connection.execute(`DESCRIBE ${expectedTable}`)
        logVerbose(`Table '${expectedTable}' structure:`, columns)
      } else {
        logWarning(`Expected table '${expectedTable}' not found`)
      }
    }
    
    return true
  } catch (error) {
    logError('Schema test failed:', error.message)
    return false
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

async function testConnectionPool() {
  log('🏊 Testing connection pool...')
  
  let pool = null
  
  try {
    pool = mysql.createPool({
      ...DB_CONFIG,
      connectionLimit: 5
    })
    
    // Test multiple concurrent connections
    const promises = Array.from({ length: 3 }, async (_, i) => {
      const connection = await pool.getConnection()
      try {
        const [rows] = await connection.execute(`SELECT ${i + 1} as connection_id`)
        logVerbose(`Connection ${i + 1} successful:`, rows[0])
        return true
      } finally {
        connection.release()
      }
    })
    
    const results = await Promise.all(promises)
    
    if (results.every(r => r === true)) {
      logSuccess('Connection pool test passed')
      return true
    } else {
      logError('Some pool connections failed')
      return false
    }
  } catch (error) {
    logError('Connection pool test failed:', error.message)
    return false
  } finally {
    if (pool) {
      await pool.end()
    }
  }
}

async function testPerformance() {
  log('⚡ Testing database performance...')
  
  let connection = null
  
  try {
    connection = await mysql.createConnection(DB_CONFIG)
    
    // Test query performance
    const iterations = 10
    const times = []
    
    for (let i = 0; i < iterations; i++) {
      const startTime = Date.now()
      await connection.execute('SELECT NOW() AS current_time')
      const endTime = Date.now()
      times.push(endTime - startTime)
    }
    
    const avgTime = times.reduce((a, b) => a + b, 0) / times.length
    const minTime = Math.min(...times)
    const maxTime = Math.max(...times)
    
    logVerbose('Performance metrics:', {
      iterations,
      averageTime: `${avgTime.toFixed(2)}ms`,
      minTime: `${minTime}ms`,
      maxTime: `${maxTime}ms`
    })
    
    if (avgTime < 1000) { // Less than 1 second average
      logSuccess(`Performance test passed (avg: ${avgTime.toFixed(2)}ms)`)
      return true
    } else {
      logWarning(`Performance may be slow (avg: ${avgTime.toFixed(2)}ms)`)
      return true // Still pass, just warn
    }
  } catch (error) {
    logError('Performance test failed:', error.message)
    return false
  } finally {
    if (connection) {
      await connection.end()
    }
  }
}

// Main test runner
async function runTests() {
  if (options.help) {
    console.log(`
Database Connectivity Test Runner

Usage: node scripts/test-database.js [options]

Options:
  --quick     Run only basic connectivity tests
  --full      Run comprehensive tests including performance
  --verbose   Show detailed output
  --help      Show this help message

Examples:
  node scripts/test-database.js --quick --verbose
  node scripts/test-database.js --full
    `)
    return
  }
  
  log('🚀 Starting database connectivity tests...')
  log('📍 Target database:', `${DB_CONFIG.host}:${DB_CONFIG.port}/${DB_CONFIG.database}`)
  
  const tests = [
    { name: 'Configuration', fn: testConfiguration },
    { name: 'Basic Connectivity', fn: testBasicConnectivity },
  ]
  
  if (!options.quick) {
    tests.push(
      { name: 'Database Schema', fn: testDatabaseSchema },
      { name: 'Connection Pool', fn: testConnectionPool }
    )
  }
  
  if (options.full) {
    tests.push(
      { name: 'Performance', fn: testPerformance }
    )
  }
  
  let passed = 0
  let failed = 0
  
  for (const test of tests) {
    try {
      log(`\n--- Running ${test.name} Test ---`)
      const result = await test.fn()
      if (result) {
        passed++
      } else {
        failed++
      }
    } catch (error) {
      logError(`Test '${test.name}' threw an error:`, error.message)
      failed++
    }
  }
  
  log('\n📊 Test Results Summary:')
  log(`✅ Passed: ${passed}`)
  log(`❌ Failed: ${failed}`)
  log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`)
  
  if (failed === 0) {
    logSuccess('All database tests passed! 🎉')
    process.exit(0)
  } else {
    logError('Some database tests failed. Please check the configuration and network connectivity.')
    process.exit(1)
  }
}

// Run the tests
runTests().catch(error => {
  logError('Test runner failed:', error)
  process.exit(1)
})
