import React from 'react'
import { APIService, type FileImport, type PaginationParams } from '../services/APIService'

// Cache interface
interface CacheEntry<T> {
  data: T
  timestamp: number
  ttl: number // Time to live in milliseconds
}

interface Cache<T> {
  [key: string]: CacheEntry<T>
}

// Lazy data state interface
export interface LazyDataState<T> {
  data: T[]
  loading: boolean
  error: string | null
  hasMore: boolean
  total: number
  page: number
  pageSize: number
}

// Simple in-memory cache with TTL
export class DataCache {
  private static instance: DataCache
  private cache: Cache<unknown> = {}
  private readonly DEFAULT_TTL = 5 * 60 * 1000 // 5 minutes

  static getInstance(): DataCache {
    if (!DataCache.instance) {
      DataCache.instance = new DataCache()
    }
    return DataCache.instance
  }

  set<T>(key: string, data: T, ttl: number = this.DEFAULT_TTL): void {
    this.cache[key] = {
      data,
      timestamp: Date.now(),
      ttl
    }
  }

  get<T>(key: string): T | null {
    const entry = this.cache[key]
    if (!entry) return null

    if (Date.now() - entry.timestamp > entry.ttl) {
      delete this.cache[key]
      return null
    }

    return entry.data as T
  }

  clear(): void {
    this.cache = {}
  }

  // Clean up expired entries
  cleanup(): void {
    const now = Date.now()
    Object.keys(this.cache).forEach(key => {
      if (now - this.cache[key].timestamp > this.cache[key].ttl) {
        delete this.cache[key]
      }
    })
  }
}

// Utility functions for data loading
export function createCacheKey(page: number, search: string, pageSize: number): string {
  return `fileImports_page${page}_search${search}_size${pageSize}`
}

export function createPaginationParams(page: number, pageSize: number, search: string): PaginationParams {
  return {
    page,
    pageSize,
    search: search || undefined
  }
}

export function createNewDataState<T>(
  response: { data: T[], total: number },
  page: number,
  pageSize: number,
  append: boolean,
  currentData: T[]
): LazyDataState<T> {
  return {
    data: append ? [...currentData, ...response.data] : response.data,
    loading: false,
    error: null,
    hasMore: (page + 1) * pageSize < response.total,
    total: response.total,
    page,
    pageSize
  }
}

// Load data with caching and error handling
export async function loadDataWithCache(
  page: number,
  search: string,
  pageSize: number,
  append: boolean,
  enableCache: boolean,
  cacheTtl: number,
  currentData: FileImport[],
  cache: DataCache,
  setState: React.Dispatch<React.SetStateAction<LazyDataState<FileImport>>>
): Promise<void> {
  const cacheKey = createCacheKey(page, search, pageSize)

  // Check cache first if enabled
  if (enableCache && !append) {
    const cachedData = cache.get<LazyDataState<FileImport>>(cacheKey)
    if (cachedData) {
      setState((prev: LazyDataState<FileImport>) => ({
        ...prev,
        ...cachedData,
        loading: false
      }))
      return
    }
  }

  const params = createPaginationParams(page, pageSize, search)
  const response = await APIService.getFileImports(params)
  const newState = createNewDataState(response, page, pageSize, append, currentData)

  setState(newState)

  // Cache the result if enabled
  if (enableCache) {
    cache.set(cacheKey, newState, cacheTtl)
  }
}

// Create action callbacks
export function createActionCallbacks(
  state: LazyDataState<FileImport>,
  searchTerm: string,
  loadData: (page?: number, search?: string, append?: boolean) => Promise<void>,
  cache: DataCache
) {
  const loadMore = () => {
    if (!state.loading && state.hasMore) {
      loadData(state.page + 1, searchTerm, true)
    }
  }

  const refresh = () => {
    cache.cleanup()
    loadData(0, searchTerm, false)
  }

  const search = (term: string) => {
    loadData(0, term, false)
  }

  const clearCache = () => {
    cache.clear()
  }

  return { loadMore, refresh, search, clearCache }
}