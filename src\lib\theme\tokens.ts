import { ThemeConfiguration } from './types';

// Production-safe logger utility
const logger = {
  info: (message: string, ...args: unknown[]) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.log(`[ThemeTokens] ${message}`, ...args);
    }
  },
  warn: (message: string, ...args: unknown[]) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.warn(`[ThemeTokens] ${message}`, ...args);
    }
  },
  error: (message: string, ...args: unknown[]) => {
    // eslint-disable-next-line no-console
    console.error(`[ThemeTokens] ${message}`, ...args);
  }
};

// Constants for duplicate strings
const COLORS_PREFIX = 'colors.';
const TYPOGRAPHY_PREFIX = 'typography.';
const SPACING_PREFIX = 'spacing.';
const SHADOWS_PREFIX = 'shadows.';
const BORDERRADIUS_PREFIX = 'borderRadius.';
const FONTFAMILY_PREFIX = 'fontFamily.';
const FONTSIZE_PREFIX = 'fontSize.';
const FONTWEIGHT_PREFIX = 'fontWeight.';
const LINEHEIGHT_PREFIX = 'lineHeight.';

// CSS Variable mapping for theme injection
export const cssVariableMap = {
  // Background colors
  '--bg-primary': `${COLORS_PREFIX}bgPrimary`,
  '--bg-secondary': `${COLORS_PREFIX}bgSecondary`,
  '--bg-tertiary': `${COLORS_PREFIX}bgTertiary`,
  '--bg-header': `${COLORS_PREFIX}bgHeader`,
  '--bg-header-group': `${COLORS_PREFIX}bgHeaderGroup`,

  // Text colors
  '--text-primary': `${COLORS_PREFIX}textPrimary`,
  '--text-secondary': `${COLORS_PREFIX}textSecondary`,
  '--text-header': `${COLORS_PREFIX}textHeader`,

  // Border colors
  '--border-color': `${COLORS_PREFIX}borderColor`,
  '--border-header': `${COLORS_PREFIX}borderHeader`,

  // Accent colors
  '--accent-color': `${COLORS_PREFIX}accentColor`,
  '--hover-bg': `${COLORS_PREFIX}hoverBg`,

  // Shadow colors
  '--shadow': `${COLORS_PREFIX}shadow`,
  '--frozen-shadow': `${COLORS_PREFIX}frozenShadow`,

  // Table-specific variables (from proven react-web-platform implementation)
  '--table-container': `${COLORS_PREFIX}bgPrimary`,
  '--table-container-elevated': `${COLORS_PREFIX}bgSecondary`,
  '--table-header': `${COLORS_PREFIX}bgHeader`,
  '--table-header-elevated': `${COLORS_PREFIX}bgHeaderGroup`,
  '--table-group-header': `${COLORS_PREFIX}bgHeaderGroup`,
  '--table-group-header-line': `${COLORS_PREFIX}borderHeader`,
  '--table-row': `${COLORS_PREFIX}bgSecondary`,
  '--table-row-hover': `${COLORS_PREFIX}hoverBg`,
  '--table-border': `${COLORS_PREFIX}borderColor`,
  '--table-shadow': `${COLORS_PREFIX}shadow`,
  '--table-shadow-elevated': `${COLORS_PREFIX}shadow`,
  '--table-freeze-shadow': `${COLORS_PREFIX}frozenShadow`,

  // Badge colors
  '--badge-grade-a': 'colors.badgeGradeA',
  '--badge-grade-a-foreground': 'colors.badgeGradeAForeground',
  '--badge-grade-a-border': 'colors.badgeGradeABorder',

  '--badge-grade-b': 'colors.badgeGradeB',
  '--badge-grade-b-foreground': 'colors.badgeGradeBForeground',
  '--badge-grade-b-border': 'colors.badgeGradeBBorder',

  '--badge-grade-c': 'colors.badgeGradeC',
  '--badge-grade-c-foreground': 'colors.badgeGradeCForeground',
  '--badge-grade-c-border': 'colors.badgeGradeCBorder',

  '--badge-grade-d': 'colors.badgeGradeD',
  '--badge-grade-d-foreground': 'colors.badgeGradeDForeground',
  '--badge-grade-d-border': 'colors.badgeGradeDBorder',

  '--badge-grade-f': 'colors.badgeGradeF',
  '--badge-grade-f-foreground': 'colors.badgeGradeFForeground',
  '--badge-grade-f-border': 'colors.badgeGradeFBorder',

  '--badge-neutral': 'colors.badgeNeutral',
  '--badge-neutral-foreground': 'colors.badgeNeutralForeground',
  '--badge-neutral-border': 'colors.badgeNeutralBorder',

  // Typography
  '--font-family-primary': `${TYPOGRAPHY_PREFIX}${FONTFAMILY_PREFIX}primary`,
  '--font-family-secondary': `${TYPOGRAPHY_PREFIX}${FONTFAMILY_PREFIX}secondary`,
  '--font-family-mono': `${TYPOGRAPHY_PREFIX}${FONTFAMILY_PREFIX}mono`,

  '--font-size-xs': `${TYPOGRAPHY_PREFIX}${FONTSIZE_PREFIX}xs`,
  '--font-size-sm': `${TYPOGRAPHY_PREFIX}${FONTSIZE_PREFIX}sm`,
  '--font-size-base': `${TYPOGRAPHY_PREFIX}${FONTSIZE_PREFIX}base`,
  '--font-size-lg': `${TYPOGRAPHY_PREFIX}${FONTSIZE_PREFIX}lg`,
  '--font-size-xl': `${TYPOGRAPHY_PREFIX}${FONTSIZE_PREFIX}xl`,
  '--font-size-2xl': `${TYPOGRAPHY_PREFIX}${FONTSIZE_PREFIX}2xl`,
  '--font-size-3xl': `${TYPOGRAPHY_PREFIX}${FONTSIZE_PREFIX}3xl`,

  '--font-weight-normal': `${TYPOGRAPHY_PREFIX}${FONTWEIGHT_PREFIX}normal`,
  '--font-weight-medium': `${TYPOGRAPHY_PREFIX}${FONTWEIGHT_PREFIX}medium`,
  '--font-weight-semibold': `${TYPOGRAPHY_PREFIX}${FONTWEIGHT_PREFIX}semibold`,
  '--font-weight-bold': `${TYPOGRAPHY_PREFIX}${FONTWEIGHT_PREFIX}bold`,

  '--line-height-tight': `${TYPOGRAPHY_PREFIX}${LINEHEIGHT_PREFIX}tight`,
  '--line-height-normal': `${TYPOGRAPHY_PREFIX}${LINEHEIGHT_PREFIX}normal`,
  '--line-height-relaxed': `${TYPOGRAPHY_PREFIX}${LINEHEIGHT_PREFIX}relaxed`,

  // Spacing
  '--spacing-xs': `${SPACING_PREFIX}xs`,
  '--spacing-sm': `${SPACING_PREFIX}sm`,
  '--spacing-md': `${SPACING_PREFIX}md`,
  '--spacing-lg': `${SPACING_PREFIX}lg`,
  '--spacing-xl': `${SPACING_PREFIX}xl`,
  '--spacing-2xl': `${SPACING_PREFIX}2xl`,
  '--spacing-3xl': `${SPACING_PREFIX}3xl`,

  // Border radius
  '--radius-none': `${BORDERRADIUS_PREFIX}none`,
  '--radius-sm': `${BORDERRADIUS_PREFIX}sm`,
  '--radius-md': `${BORDERRADIUS_PREFIX}md`,
  '--radius-lg': `${BORDERRADIUS_PREFIX}lg`,
  '--radius-xl': `${BORDERRADIUS_PREFIX}xl`,
  '--radius-full': `${BORDERRADIUS_PREFIX}full`,

  // Shadows
  '--shadow-sm': `${SHADOWS_PREFIX}sm`,
  '--shadow-md': `${SHADOWS_PREFIX}md`,
  '--shadow-lg': `${SHADOWS_PREFIX}lg`,
  '--shadow-xl': `${SHADOWS_PREFIX}xl`,
} as const;

// shadcn/ui CSS variable mappings for theme integration
export const shadcnVariableMap = {
  // Background colors
  '--background': `${COLORS_PREFIX}bgPrimary`,
  '--foreground': `${COLORS_PREFIX}textPrimary`,
  '--card': `${COLORS_PREFIX}bgSecondary`,
  '--card-foreground': `${COLORS_PREFIX}textPrimary`,
  '--popover': `${COLORS_PREFIX}bgSecondary`,
  '--popover-foreground': `${COLORS_PREFIX}textPrimary`,
  '--primary': `${COLORS_PREFIX}accentColor`,
  '--primary-foreground': `${COLORS_PREFIX}bgPrimary`,
  '--secondary': `${COLORS_PREFIX}bgTertiary`,
  '--secondary-foreground': `${COLORS_PREFIX}textPrimary`,
  '--muted': `${COLORS_PREFIX}bgTertiary`,
  '--muted-foreground': `${COLORS_PREFIX}textSecondary`,
  '--accent': `${COLORS_PREFIX}bgTertiary`,
  '--accent-foreground': `${COLORS_PREFIX}textPrimary`,
  '--border': `${COLORS_PREFIX}borderColor`,
  '--input': `${COLORS_PREFIX}borderColor`,
  '--ring': `${COLORS_PREFIX}accentColor`,
  // Keep destructive colors static for now (red variants)
  '--destructive': '#dc2626',
  '--destructive-foreground': '#ffffff',
} as const;

// Helper function to get nested object value by path
export function getNestedValue(obj: Record<string, unknown> | ThemeConfiguration, path: string): unknown {
  return path.split('.').reduce((current: unknown, key: string) => {
    if (current && typeof current === 'object' && current !== null && key in current) {
      return (current as Record<string, unknown>)[key];
    }
    return undefined;
  }, obj);
}

// Helper function to convert hex color to HSL format for shadcn/ui
export function hexToHsl(hex: string): string {
  // Remove # if present and validate
  hex = hex.replace('#', '');

  if (hex.length !== 6) {
    throw new Error(`Invalid hex color: ${hex}. Expected 6 characters.`);
  }

  // Parse RGB values
  const r = parseInt(hex.substring(0, 2), 16) / 255;
  const g = parseInt(hex.substring(2, 4), 16) / 255;
  const b = parseInt(hex.substring(4, 6), 16) / 255;

  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  let h = 0;
  let s = 0;
  const l = (max + min) / 2;

  if (max !== min) {
    const d = max - min;
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min);

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break;
      case g: h = (b - r) / d + 2; break;
      case b: h = (r - g) / d + 4; break;
    }
    h /= 6;
  }

  // Convert to degrees and percentages
  h = Math.round(h * 360);
  s = Math.round(s * 100);
  const lPercent = Math.round(l * 100);

  return `${h} ${s}% ${lPercent}%`;
}

// Generate CSS variables from theme configuration
export function generateCSSVariables(theme: ThemeConfiguration): Record<string, string> {
  const cssVariables: Record<string, string> = {};

  Object.entries(cssVariableMap).forEach(([cssVar, themePath]) => {
    const value = getNestedValue(theme, themePath);
    if (value !== undefined) {
      cssVariables[cssVar] = value;
    }
  });

  return cssVariables;
}

// Generate shadcn/ui CSS variables from theme configuration
export function generateShadcnVariables(theme: ThemeConfiguration): Record<string, string> {
  const cssVariables: Record<string, string> = {};

  Object.entries(shadcnVariableMap).forEach(([cssVar, themePath]) => {
    const value = getNestedValue(theme, themePath);
    if (value !== undefined) {
      // Convert hex colors to HSL format for shadcn/ui
      if (typeof value === 'string' && value.startsWith('#') && value.length >= 7) {
        try {
          cssVariables[cssVar] = hexToHsl(value);
        } catch (error) {
          logger.warn(`Failed to convert ${value} to HSL for ${cssVar}:`, error);
          cssVariables[cssVar] = value;
        }
      } else {
        cssVariables[cssVar] = value;
      }
    }
  });

  return cssVariables;
}

// Inject CSS variables into document root
export function injectCSSVariables(theme: ThemeConfiguration): void {
  const cssVariables = generateCSSVariables(theme);
  const shadcnVariables = generateShadcnVariables(theme);
  const root = document.documentElement;

  // Inject custom theme variables
  Object.entries(cssVariables).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });

  // Inject shadcn/ui variables
  Object.entries(shadcnVariables).forEach(([property, value]) => {
    root.style.setProperty(property, value);
  });
}

// Remove all theme CSS variables
export function removeCSSVariables(): void {
  const root = document.documentElement;

  // Remove custom theme variables
  Object.keys(cssVariableMap).forEach((property) => {
    root.style.removeProperty(property);
  });

  // Remove shadcn/ui variables
  Object.keys(shadcnVariableMap).forEach((property) => {
    root.style.removeProperty(property);
  });
}