import { MySQLDataService, getMySQLConfigStatus } from './mysqlData';

// Production-safe logger utility
const logger = {
  info: (message: string, ...args: unknown[]) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.log(`[MySQLTestUtility] ${message}`, ...args);
    }
  },
  warn: (message: string, ...args: unknown[]) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.warn(`[MySQLTestUtility] ${message}`, ...args);
    }
  },
  error: (message: string, ...args: unknown[]) => {
    // eslint-disable-next-line no-console
    console.error(`[MySQLTestUtility] ${message}`, ...args);
  }
};

/**
 * MySQL Integration Test Utility
 * Use this to verify MySQL configuration and connectivity
 */

const ERROR_MESSAGES = {
  UNKNOWN_ERROR: 'Unknown error',
  CONFIG_TEST_FAILED: 'Configuration test failed',
  DATABASE_CONNECTION_FAILED: 'Database connection failed',
  DATA_RETRIEVAL_FAILED: 'Data retrieval failed'
} as const;

export interface MySQLTestResult {
  success: boolean;
  message: string;
  details?: Record<string, unknown>;
  timestamp: string;
}

export class MySQLTestUtility {
  /**
   * Test MySQL configuration
   */
  static async testConfiguration(): Promise<MySQLTestResult> {
    try {
      const config = getMySQLConfigStatus();

      if (!config.isConfigured) {
        return {
          success: false,
          message: 'MySQL configuration incomplete',
          details: {
            hasHost: config.hasHost,
            hasUser: config.hasUser,
            hasPassword: config.hasPassword,
            hasDatabase: config.hasDatabase,
            hasPort: config.hasPort
          },
          timestamp: new Date().toISOString()
        };
      }

      return {
        success: true,
        message: 'MySQL configuration is valid',
        details: config,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        message: `${ERROR_MESSAGES.CONFIG_TEST_FAILED}: ${error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR}`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Test MySQL database connectivity
   */
  static async testConnectivity(): Promise<MySQLTestResult> {
    try {
      const isConnected = await MySQLDataService.testConnection();

      if (!isConnected) {
        return {
          success: false,
          message: 'Database connection failed',
          timestamp: new Date().toISOString()
        };
      }

      return {
        success: true,
        message: 'Successfully connected to MySQL database',
        details: {
          connectionSuccessful: true
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        message: `${ERROR_MESSAGES.DATABASE_CONNECTION_FAILED}: ${error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR}`,
        details: { error },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Test data retrieval
   */
  static async testDataRetrieval(): Promise<MySQLTestResult> {
    try {
      const result = await MySQLDataService.getFileImports({
        page: 0,
        pageSize: 5
      });

      return {
        success: true,
        message: `Successfully retrieved ${result.data.length} records`,
        details: {
          recordCount: result.data.length,
          totalRecords: result.total,
          hasData: result.data.length > 0
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        success: false,
        message: `${ERROR_MESSAGES.DATA_RETRIEVAL_FAILED}: ${error instanceof Error ? error.message : ERROR_MESSAGES.UNKNOWN_ERROR}`,
        details: { error },
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
    * Run all tests
    */
   static async runAllTests(): Promise<{
     configuration: MySQLTestResult;
     connectivity: MySQLTestResult;
     dataRetrieval: MySQLTestResult;
     overall: {
       success: boolean;
       message: string;
       timestamp: string;
     };
   }> {
     logger.info('Running MySQL Integration Tests...');

     const configuration = await this.testConfiguration();
     logger.info(`Configuration Test: ${configuration.success ? 'PASSED' : 'FAILED'} - ${configuration.message}`);

     const connectivity = configuration.success ? await this.testConnectivity() : {
       success: false,
       message: 'Skipped - configuration failed',
       timestamp: new Date().toISOString()
     };
     logger.info(`Connectivity Test: ${connectivity.success ? 'PASSED' : 'FAILED'} - ${connectivity.message}`);

     const dataRetrieval = connectivity.success ? await this.testDataRetrieval() : {
       success: false,
       message: 'Skipped - connectivity failed',
       timestamp: new Date().toISOString()
     };
     logger.info(`Data Retrieval Test: ${dataRetrieval.success ? 'PASSED' : 'FAILED'} - ${dataRetrieval.message}`);

     const overall = {
       success: configuration.success && connectivity.success && dataRetrieval.success,
       message: this.getOverallMessage(configuration, connectivity, dataRetrieval),
       timestamp: new Date().toISOString()
     };

     logger.info(`Overall Result: ${overall.success ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED'} - ${overall.message}`);
     return {
       configuration,
       connectivity,
       dataRetrieval,
       overall
     };
   }

  private static getOverallMessage(config: MySQLTestResult, connect: MySQLTestResult, data: MySQLTestResult): string {
    if (config.success && connect.success && data.success) {
      return '✅ All MySQL integration tests passed!';
    }

    const failures = [];
    if (!config.success) failures.push('configuration');
    if (!connect.success) failures.push('connectivity');
    if (!data.success) failures.push('data retrieval');

    return `❌ Failed tests: ${failures.join(', ')}`;
  }
}

// Browser console utility for easy testing
if (typeof window !== 'undefined') {
  // Make it available in browser console
  (window as unknown as Record<string, unknown>).mysqlTest = MySQLTestUtility;

  if (import.meta.env.DEV) {
    // eslint-disable-next-line no-console
    console.log(`
🔧 MySQL Test Utility Available!
Run these commands in the browser console:

// Test configuration
mysqlTest.testConfiguration().then(console.log)

// Test connectivity
mysqlTest.testConnectivity().then(console.log)

// Test data retrieval
mysqlTest.testDataRetrieval().then(console.log)

// Run all tests
mysqlTest.runAllTests().then(console.log)
    `);
  }
}