import {
  GeneratedTests,
  ComponentAnalysis,
  TestScenario,
  AIEngine,
  CodeAnalyzer
} from './types';
import { readFileSync } from 'fs';

export class AITestGenerator {
  private aiEngine: AIEngine;
  private codeAnalyzer: CodeAnalyzer;

  constructor(aiEngine: <PERSON>Engine, codeAnalyzer: CodeAnalyzer) {
    this.aiEngine = aiEngine;
    this.codeAnalyzer = codeAnalyzer;
  }

  public async generateTestsForComponent(
    componentPath: string
  ): Promise<GeneratedTests> {
    // Analyze component code
    const componentAnalysis = await this.codeAnalyzer.analyze(componentPath);

    // Generate test scenarios based on component analysis
    const testScenarios = await this.aiEngine.generateTestScenarios(componentAnalysis);

    // Generate test code
    const testCode = await this.generateTestCode(testScenarios, componentAnalysis);

    // Validate generated tests
    const validation = await this.validateGeneratedTests(testCode, componentAnalysis);

    return {
      testCode,
      coverage: validation.coverage,
      scenarios: testScenarios,
      validation
    };
  }

  private async generateTestCode(
    scenarios: TestScenario[],
    analysis: ComponentAnalysis
  ): Promise<string> {
    const imports = this.generateImports(analysis);
    const testCases = await this.generateTestCases(scenarios, analysis);
    const setupCode = this.generateSetupCode(analysis);

    return `${imports}

describe('${analysis.componentName}', () => {
  ${setupCode}

  ${testCases}
});

// Helper functions
${this.generateHelperFunctions(analysis)}
`;
  }

  private generateImports(analysis: ComponentAnalysis): string {
    const imports = [
      `import { render, screen, fireEvent, waitFor } from '@testing-library/react';`,
      `import { ${analysis.componentName} } from '${analysis.importPath}';`
    ];

    // Add additional imports based on component dependencies
    // eslint-disable-next-line sonarjs/no-duplicate-string
    const REACT_ROUTER_DEPENDENCY = 'react-router';
    const AXIOS_DEPENDENCY = 'axios';
    const BROWSER_ROUTER_IMPORT = `import { BrowserRouter } from 'react-router-dom';`;
    const AXIOS_IMPORT = `import axios from 'axios';`;

    if (analysis.dependencies.some(dep => dep.includes(REACT_ROUTER_DEPENDENCY))) {
      imports.push(BROWSER_ROUTER_IMPORT);
    }

    if (analysis.dependencies.some(dep => dep.includes(AXIOS_DEPENDENCY))) {
      imports.push(AXIOS_IMPORT);
    }

    return imports.join('\n');
  }

  private generateSetupCode(analysis: ComponentAnalysis): string {
    const AXIOS_DEPENDENCY = 'axios';
    let setup = '';

    // Mock external dependencies
    if (analysis.dependencies.some(dep => dep.includes(AXIOS_DEPENDENCY))) {
      setup += `
  let mockAxios: jest.Mocked<typeof axios>;

  beforeEach(() => {
    mockAxios = axios as jest.Mocked<typeof axios>;
    jest.clearAllMocks();
  });`;
    }

    // Setup for components that need routing
    if (analysis.dependencies.some(dep => dep.includes('react-router'))) {
      setup += `

  const renderWithRouter = (component: React.ReactElement) => {
    return render(<BrowserRouter>{component}</BrowserRouter>);
  };`;
    }

    return setup;
  }

  private async generateTestCases(
    scenarios: TestScenario[],
    analysis: ComponentAnalysis
  ): Promise<string> {
    const testCases: string[] = [];

    for (const scenario of scenarios) {
      const testCase = await this.generateTestCase(scenario, analysis);
      testCases.push(testCase);
    }

    return testCases.join('\n\n  ');
  }

  private async generateTestCase(
    scenario: TestScenario,
    analysis: ComponentAnalysis
  ): Promise<string> {
    return `
  it('${scenario.description}', async () => {
    ${await this.generateTestBody(scenario, analysis)}
  });`;
  }

  private async generateTestBody(
    scenario: TestScenario,
    analysis: ComponentAnalysis
  ): Promise<string> {
    // This would use AI to generate the actual test implementation
    // For now, return a basic template

    let body = '';

    // Generate props based on component analysis
    const props = this.generateProps(analysis);
    body += `const props = ${JSON.stringify(props, null, 2)};`;

    // Render component
    if (analysis.dependencies.some(dep => dep.includes('react-router'))) {
      body += `
    renderWithRouter(<${analysis.componentName} {...props} />);`;
    } else {
      body += `
    render(<${analysis.componentName} {...props} />);`;
    }

    // Add test assertions based on scenario
    body += `
    ${this.generateAssertions(scenario, analysis)}`;

    return body;
  }

  private generateProps(analysis: ComponentAnalysis): Record<string, unknown> {
    const props: Record<string, unknown> = {};

    analysis.props.forEach(prop => {
      if (!prop.required) return;

      switch (prop.type.toLowerCase()) {
        case 'string':
          props[prop.name] = `test-${prop.name}`;
          break;
        case 'number':
          props[prop.name] = 42;
          break;
        case 'boolean':
          props[prop.name] = true;
          break;
        case 'function':
          props[prop.name] = `jest.fn()`;
          break;
        default:
          props[prop.name] = `mock-${prop.name}`;
      }
    });

    return props;
  }

  private generateAssertions(scenario: TestScenario, analysis: ComponentAnalysis): string {
    // Generate basic assertions based on scenario type
    const assertions: string[] = [];

    if (scenario.description.toLowerCase().includes('render')) {
      assertions.push(`expect(screen.getByTestId('${analysis.componentName.toLowerCase()}')).toBeInTheDocument();`);
    }

    if (scenario.description.toLowerCase().includes('click')) {
      assertions.push(`
    const button = screen.getByRole('button');
    fireEvent.click(button);
    // Add expectations for click behavior`);
    }

    if (scenario.description.toLowerCase().includes('input')) {
      assertions.push(`
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'test input' } });
    expect(input).toHaveValue('test input');`);
    }

    return assertions.join('\n    ');
  }

  private generateHelperFunctions(analysis: ComponentAnalysis): string {
    return `
function createMockProps(overrides = {}) {
  return {
    ${analysis.props.map(prop => `${prop.name}: ${this.getDefaultValue(prop)}`).join(',\n    ')},
    ...overrides
  };
}

function renderComponent(props = {}) {
  const defaultProps = createMockProps();
  const finalProps = { ...defaultProps, ...props };
  return render(<${analysis.componentName} {...finalProps} />);
}`;
  }

  private getDefaultValue(prop: ComponentAnalysis['props'][0]): string {
    switch (prop.type.toLowerCase()) {
      case 'string':
        return `'test-${prop.name}'`;
      case 'number':
        return '42';
      case 'boolean':
        return 'true';
      case 'function':
        return 'jest.fn()';
      default:
        return `'mock-${prop.name}'`;
    }
  }

  private async validateGeneratedTests(
    testCode: string,
    analysis: ComponentAnalysis
  ): Promise<GeneratedTests['validation']> {
    // Basic validation - check for syntax and common patterns
    const syntaxValid = this.validateSyntax(testCode);
    const importsValid = this.validateImports(testCode, analysis);
    const coverage = this.estimateCoverage(testCode, analysis);

    return {
      syntaxValid,
      importsValid,
      coverage
    };
  }

  private validateSyntax(code: string): boolean {
    try {
      // Basic syntax check by attempting to parse
      new Function(code);
      return true;
    } catch {
      return false;
    }
  }

  private validateImports(code: string, analysis: ComponentAnalysis): boolean {
    // Check if required imports are present
    const requiredImports = [
      '@testing-library/react',
      analysis.importPath
    ];

    return requiredImports.every(imp =>
      code.includes(`from '${imp}'`) || code.includes(`from "${imp}"`)
    );
  }

  private estimateCoverage(code: string, analysis: ComponentAnalysis): number {
    // Simple coverage estimation based on test patterns
    let coverage = 0;

    if (code.includes('render(')) coverage += 20;
    if (code.includes('screen.getBy')) coverage += 20;
    if (code.includes('expect(')) coverage += 20;
    if (code.includes('fireEvent.')) coverage += 20;
    if (code.includes(analysis.componentName)) coverage += 20;

    return Math.min(coverage, 100);
  }
}

// Basic implementations of the engine interfaces
export class BasicAIEngine implements AIEngine {
  public async generateTestScenarios(analysis: ComponentAnalysis): Promise<TestScenario[]> {
    // Generate basic test scenarios based on component analysis
    const scenarios: TestScenario[] = [
      {
        description: 'renders without crashing',
        testCode: '',
        priority: 'high'
      },
      {
        description: 'displays correct content',
        testCode: '',
        priority: 'high'
      }
    ];

    // Add scenarios based on props
    analysis.props.forEach(prop => {
      if (prop.required) {
        scenarios.push({
          description: `handles ${prop.name} prop correctly`,
          testCode: '',
          priority: 'medium'
        });
      }
    });

    // Add interaction scenarios
    if (analysis.methods.some(method => method.toLowerCase().includes('click'))) {
      scenarios.push({
        description: 'handles click events',
        testCode: '',
        priority: 'high'
      });
    }

    return scenarios;
  }

  public async fillTemplate(template: string, data: Record<string, unknown>): Promise<string> {
    // Simple template filling
    let result = template;

    Object.entries(data).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value));
    });

    return result;
  }
}

export class BasicCodeAnalyzer implements CodeAnalyzer {
  public async analyze(filePath: string): Promise<ComponentAnalysis> {
    const content = readFileSync(filePath, 'utf-8');

    // Basic analysis - extract component name and props
    const componentName = this.extractComponentName(content);
    const props = this.extractProps(content);
    const methods = this.extractMethods(content);
    const dependencies = this.extractDependencies(content);

    return {
      componentName,
      importPath: filePath.replace(/\\/g, '/').replace(/^src\//, './'),
      props,
      methods,
      dependencies
    };
  }

  private extractComponentName(content: string): string {
    // Look for export default or export const
    const exportMatch = content.match(/export\s+(?:default\s+)?(?:const|function)\s+(\w+)/);
    if (exportMatch) return exportMatch[2];

    // Look for function declaration
    const functionMatch = content.match(/function\s+(\w+)/);
    if (functionMatch) return functionMatch[1];

    // Look for arrow function
    const arrowMatch = content.match(/const\s+(\w+)\s*=/);
    if (arrowMatch) return arrowMatch[1];

    return 'UnknownComponent';
  }

  private extractProps(content: string): ComponentAnalysis['props'] {
    // Look for prop types or interface definitions
    const props: ComponentAnalysis['props'] = [];

    // Simple regex to find prop patterns
    const propPatterns = [
      /(\w+):\s*(\w+)/g, // name: type
      /(\w+)\??:\s*(\w+)/g // name?: type
    ];

    propPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const [, name, type] = match;
        if (!props.some(p => p.name === name)) {
          props.push({
            name,
            type,
            required: !match[0].includes('?')
          });
        }
      }
    });

    return props;
  }

  private extractMethods(content: string): string[] {
    const methods: string[] = [];

    // Look for function calls that might indicate methods
    const methodPatterns = [
      /\.(\w+)\(/g, // object.method(
      /(\w+)\s*\(/g // functionName(
    ];

    methodPatterns.forEach(pattern => {
      let match;
      while ((match = pattern.exec(content)) !== null) {
        const method = match[1];
        if (!methods.includes(method) && !['console', 'setTimeout', 'setInterval'].includes(method)) {
          methods.push(method);
        }
      }
    });

    return methods;
  }

  private extractDependencies(content: string): string[] {
    const dependencies: string[] = [];

    // Look for import statements
    const importPattern = /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g;
    let match;
    while ((match = importPattern.exec(content)) !== null) {
      dependencies.push(match[1]);
    }

    return dependencies;
  }
}