import * as Sentry from '@sentry/react'
import { config } from '@/config/environments'

// Declare gtag on window
declare global {
  interface Window {
    gtag: (command: string, targetId: string, config?: Record<string, unknown>) => void
  }
}

export function initializeMonitoring() {
  // Sentry initialization
  if (config.sentryDsn) {
    Sentry.init({
      dsn: config.sentryDsn,
      environment: config.environment,
      tracesSampleRate: config.environment === 'production' ? 0.1 : 1.0,
    })
  }

  // Performance monitoring
  if (config.environment === 'production') {
    // Web Vitals tracking - TODO: Implement when web-vitals is properly configured
    // Performance monitoring enabled
  }
}

export function logError(error: Error, context?: Record<string, unknown>) {
  if (config.sentryDsn) {
    Sentry.captureException(error, {
      tags: {
        environment: config.environment,
      },
      extra: context,
    })
  }
}

export function logEvent(event: string, properties?: Record<string, unknown>) {
  // Send to analytics service if configured
  if (config.analyticsId && window.gtag) {
    window.gtag('event', event, properties)
  }
}