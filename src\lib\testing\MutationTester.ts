import { Mutant, MutationTestResults, TestRunner } from './types';
import { readFileSync, writeFileSync, unlinkSync, readdirSync, statSync } from 'fs';
import { execSync } from 'child_process';
import { tmpdir } from 'os';
import { join } from 'path';

export class MutationTester {
  private mutants: Mutant[] = [];
  private testRunner: TestRunner;
  private sourceDir: string;
  private testDir: string;

  // Mutation operator constants
  private static readonly ARITHMETIC_OP = 'arithmetic-operator-replacement';
  private static readonly BOOLEAN_OP = 'boolean-operator-replacement';
  private static readonly CONDITIONAL_OP = 'conditional-operator-replacement';
  private static readonly RELATIONAL_OP = 'relational-operator-replacement';
  private static readonly UNARY_OP = 'unary-operator-replacement';

  constructor(testRunner: TestRunner, sourceDir = 'src', testDir = 'src/__tests__') {
    this.testRunner = testRunner;
    this.sourceDir = sourceDir;
    this.testDir = testDir;
  }

  public async runMutationTesting(files?: string[]): Promise<MutationTestResults> {
    const targetFiles = files || await this.getSourceFiles();

    // Generate mutants for all target files
    this.mutants = [];
    for (const file of targetFiles) {
      const fileMutants = await this.generateMutantsForFile(file);
      this.mutants.push(...fileMutants);
    }

    // Run tests against each mutant
    const results = await this.testMutants();

    // Calculate mutation score
    const score = this.calculateMutationScore(results);

    return {
      score,
      killedMutants: results.filter(r => r.killed).length,
      survivedMutants: results.filter(r => !r.killed).map(r => r.mutant),
      equivalentMutants: results.filter(r => r.equivalent).map(r => r.mutant),
      totalMutants: this.mutants.length
    };
  }

  private async getSourceFiles(): Promise<string[]> {
    // Get all TypeScript files in source directory
    const files: string[] = [];
    const walk = (dir: string) => {
      const items = readdirSync(dir);
      for (const item of items) {
        const fullPath = join(dir, item);
        const stat = statSync(fullPath);
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
          walk(fullPath);
        } else if (item.endsWith('.ts') || item.endsWith('.tsx')) {
          files.push(fullPath);
        }
      }
    };
    walk(this.sourceDir);
    return files;
  }

  private async generateMutantsForFile(filePath: string): Promise<Mutant[]> {
    const content = readFileSync(filePath, 'utf-8');
    const mutants: Mutant[] = [];

    const operators = [
      MutationTester.ARITHMETIC_OP,
      MutationTester.BOOLEAN_OP,
      MutationTester.CONDITIONAL_OP,
      MutationTester.RELATIONAL_OP,
      MutationTester.UNARY_OP
    ];

    for (const operator of operators) {
      const operatorMutants = this.applyMutationOperator(content, filePath, operator);
      mutants.push(...operatorMutants);
    }

    return mutants;
  }

  private applyMutationOperator(
    content: string,
    filePath: string,
    operator: string
  ): Mutant[] {
    const mutants: Mutant[] = [];
    const lines = content.split('\n');

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const mutations = this.generateMutationsForLine(line, operator);

      mutations.forEach((mutation, mutationIndex) => {
        const mutatedContent = [...lines];
        mutatedContent[i] = mutation;
        const fullMutatedCode = mutatedContent.join('\n');

        mutants.push({
          id: `${filePath}:${i + 1}:${operator}:${mutationIndex}`,
          originalCode: line,
          mutatedCode: fullMutatedCode,
          operator,
          location: {
            file: filePath,
            line: i + 1,
            column: 0
          }
        });
      });
    }

    return mutants;
  }

  private generateMutationsForLine(line: string, operator: string): string[] {
    const mutations: string[] = [];

    switch (operator) {
      case MutationTester.ARITHMETIC_OP:
        mutations.push(
          ...this.replaceOperators(line, ['+', '-', '*', '/', '%'], ['-', '+', '/', '*', '*'])
        );
        break;

      case MutationTester.BOOLEAN_OP:
        mutations.push(
          ...this.replaceOperators(line, ['&&', '||'], ['||', '&&'])
        );
        break;

      case MutationTester.CONDITIONAL_OP:
        mutations.push(
          ...this.replaceOperators(line, ['===', '!==', '==', '!='], ['!==', '===', '!=', '=='])
        );
        break;

      case MutationTester.RELATIONAL_OP:
        mutations.push(
          ...this.replaceOperators(line, ['<', '>', '<=', '>='], ['<=', '>=', '>', '<'])
        );
        break;

      case MutationTester.UNARY_OP:
        mutations.push(
          ...this.replaceOperators(line, ['!', '-'], ['', '+'])
        );
        break;
    }

    return mutations;
  }

  private replaceOperators(line: string, originals: string[], replacements: string[]): string[] {
    return originals
      .map((original, index) => {
        if (line.includes(original)) {
          const replacement = replacements[index];
          const mutated = line.replace(new RegExp(this.escapeRegExp(original), 'g'), replacement);
          return mutated !== line ? mutated : null;
        }
        return null;
      })
      .filter((mutation): mutation is string => mutation !== null);
  }

  private escapeRegExp(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  private async testMutants(): Promise<Array<{ mutant: Mutant; killed: boolean; equivalent?: boolean }>> {
    const results: Array<{ mutant: Mutant; killed: boolean; equivalent?: boolean }> = [];

    for (const mutant of this.mutants) {
      const killed = await this.testMutant(mutant);
      results.push({ mutant, killed });
    }

    return results;
  }

  private async testMutant(mutant: Mutant): Promise<boolean> {
    const tempFile = join(tmpdir(), `mutant-${Date.now()}-${Math.random()}.ts`);

    try {
      // Create temporary file with mutated code
      writeFileSync(tempFile, mutant.mutatedCode, 'utf-8');

      // Run tests
      const testResult = await this.runTestsWithMutant(mutant.location.file, tempFile);

      return !testResult; // If tests still pass, mutant survived (not killed)
    } catch {
      // If there's a syntax error or compilation error, consider mutant killed
      return true;
    } finally {
      // Clean up temporary file
      try {
        unlinkSync(tempFile);
      } catch {
        // Ignore cleanup errors
      }
    }
  }

  private async runTestsWithMutant(originalFile: string, mutantFile: string): Promise<boolean> {
    // This is a simplified implementation
    // In a real scenario, you'd need to:
    // 1. Replace the original file with the mutant
    // 2. Run the test suite
    // 3. Restore the original file

    try {
      // For now, we'll just check if the mutant code compiles
      execSync(`npx tsc --noEmit ${mutantFile}`, { stdio: 'pipe' });
      return true; // Tests would pass if code compiles
    } catch {
      return false; // Tests fail if code doesn't compile
    }
  }

  private calculateMutationScore(
    results: Array<{ mutant: Mutant; killed: boolean; equivalent?: boolean }>
  ): number {
    const killed = results.filter(r => r.killed && !r.equivalent).length;
    const total = results.filter(r => !r.equivalent).length;

    return total > 0 ? (killed / total) * 100 : 0;
  }

  public getMutants(): Mutant[] {
    return this.mutants;
  }

  public getMutationOperators(): string[] {
    return [
      MutationTester.ARITHMETIC_OP,
      MutationTester.BOOLEAN_OP,
      MutationTester.CONDITIONAL_OP,
      MutationTester.RELATIONAL_OP,
      MutationTester.UNARY_OP
    ];
  }
}