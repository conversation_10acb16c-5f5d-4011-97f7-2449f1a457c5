// Production-safe logger utility
const logger = {
  info: (message: string, ...args: unknown[]) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.log(`[APIDataService] ${message}`, ...args);
    }
  },
  warn: (message: string, ...args: unknown[]) => {
    if (import.meta.env.DEV) {
      // eslint-disable-next-line no-console
      console.warn(`[APIDataService] ${message}`, ...args);
    }
  },
  error: (message: string, ...args: unknown[]) => {
    // eslint-disable-next-line no-console
    console.error(`[APIDataService] ${message}`, ...args);
  }
};

// API Data Service - HTTP client for backend API
export interface FileImport {
  id: number;
  filename: string;
  file_type: string;
  upload_date: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  records_count: number;
  processed_records: number;
  errors_count: number;
  uploaded_by: string;
  file_size: string;
  created_at: string;
  updated_at: string;
}

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

// API Service class for data operations
export class APIDataService {
  private static async apiRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;

    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      logger.error(`API Request failed: ${url}`, error);
      throw error;
    }
  }

  /**
   * Get file imports with pagination and search
   */
  static async getFileImports(params: {
    page: number;
    pageSize: number;
    search?: string;
    status?: string;
  }): Promise<{
    data: FileImport[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    const { page, pageSize, search, status } = params;
    const queryParams = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString(),
    });

    if (search) queryParams.append('search', search);
    if (status) queryParams.append('status', status);

    logger.info('Fetching file imports from API...', { page, pageSize, search, status });

    return this.apiRequest(`/file-imports?${queryParams}`);
  }

  /**
   * Get a single file import by ID
   */
  static async getFileImport(id: number): Promise<FileImport | null> {
    try {
      return await this.apiRequest<FileImport>(`/file-imports/${id}`);
    } catch (error) {
      if (error instanceof Error && error.message.includes('404')) {
        return null;
      }
      throw error;
    }
  }

  /**
   * Create a new file import
   */
  static async createFileImport(fileImport: Omit<FileImport, 'id' | 'created_at' | 'updated_at'>): Promise<FileImport> {
    return this.apiRequest<FileImport>('/file-imports', {
      method: 'POST',
      body: JSON.stringify(fileImport),
    });
  }

  /**
   * Update an existing file import
   */
  static async updateFileImport(id: number, updates: Partial<Omit<FileImport, 'id' | 'created_at'>>): Promise<FileImport> {
    return this.apiRequest<FileImport>(`/file-imports/${id}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  /**
   * Delete a file import
   */
  static async deleteFileImport(id: number): Promise<void> {
    await this.apiRequest(`/file-imports/${id}`, {
      method: 'DELETE',
    });
  }

  /**
    * Export data in CSV or JSON format
    */
   static async exportData(format: 'csv' | 'json', search?: string): Promise<unknown> {
     const queryParams = new URLSearchParams({ format });
     if (search) queryParams.append('search', search);

     const url = `${API_BASE_URL}/export?${queryParams}`;

     if (format === 'csv') {
       // For CSV, we need to handle the download differently
       const response = await fetch(url);
       if (!response.ok) {
         throw new Error(`Export failed: ${response.statusText}`);
       }
       return await response.text();
     } else {
       return this.apiRequest(`/export?${queryParams}`);
     }
   }

  /**
   * Get statistics for dashboard
   */
  static async getStatistics(): Promise<{
    totalImports: number;
    completedImports: number;
    failedImports: number;
    totalRecords: number;
  }> {
    return this.apiRequest('/statistics');
  }

  /**
    * Test API connection
    */
   static async testConnection(): Promise<boolean> {
     try {
       await this.apiRequest('/test-connection');
       logger.info('API connection successful');
       return true;
     } catch (error) {
       logger.error('API connection failed:', error);
       return false;
     }
   }

  /**
   * Health check
   */
  static async healthCheck(): Promise<{ status: string; timestamp: string }> {
    return this.apiRequest('/health');
  }
}

// Utility function to check API configuration
export const checkAPIConfiguration = (): boolean => {
  const baseUrl = import.meta.env.VITE_API_BASE_URL;
  return !!baseUrl;
};

// Utility function to get API configuration status
export const getAPIConfigStatus = () => {
  return {
    hasBaseUrl: !!import.meta.env.VITE_API_BASE_URL,
    isConfigured: checkAPIConfiguration(),
    config: {
      baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api',
    }
  };
};