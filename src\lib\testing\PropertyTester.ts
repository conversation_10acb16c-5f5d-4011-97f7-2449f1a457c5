import { PropertyTestResult, TestCaseResult, PropertyGenerators } from './types';

export class PropertyTester {
  private generators: PropertyGenerators;
  private maxIterations: number;
  private seed: number;

  constructor(generators: PropertyGenerators = {}, maxIterations = 100, seed = Date.now()) {
    this.generators = {
      // Default generators
      integer: {
        generate: () => Math.floor(Math.random() * 201) - 100, // -100 to 100
        shrink: (value: number) => value === 0 ? 0 : value > 0 ? value - 1 : value + 1
      },
      positiveInteger: {
        generate: () => Math.floor(Math.random() * 100) + 1, // 1 to 100
        shrink: (value: number) => Math.max(1, value - 1)
      },
      string: {
        generate: () => {
          const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
          const length = Math.floor(Math.random() * 20) + 1;
          let result = '';
          for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
          }
          return result;
        },
        shrink: (value: string) => value.length > 1 ? value.slice(0, -1) : ''
      },
      boolean: {
        generate: () => Math.random() > 0.5,
        shrink: () => false // Always shrink to false
      },
      array: {
        generate: () => {
          const length = Math.floor(Math.random() * 10);
          const result = [];
          for (let i = 0; i < length; i++) {
            result.push(Math.floor(Math.random() * 100));
          }
          return result;
        },
        shrink: (value: number[]) => value.length > 0 ? value.slice(0, -1) : []
      },
      ...generators
    };
    this.maxIterations = maxIterations;
    this.seed = seed;
    this.setSeed(seed);
  }

  public async testProperty<T>(
    property: (input: T) => boolean | Promise<boolean>,
    generator: Generator<T> | string,
    iterations: number = this.maxIterations
  ): Promise<PropertyTestResult> {
    const actualGenerator = typeof generator === 'string' ? this.generators[generator] : generator;

    if (!actualGenerator) {
      throw new Error(`Generator '${generator}' not found`);
    }

    const results: TestCaseResult[] = [];

    for (let i = 0; i < iterations; i++) {
      const input = actualGenerator.generate();
      const result = await this.runPropertyTest(property, input);

      results.push({
        input,
        passed: result,
        counterexample: result ? null : input
      });

      if (!result) {
        // Found counterexample, shrink it
        const shrunk = await this.shrinkCounterexample(input, property, actualGenerator);
        return {
          passed: false,
          counterexample: shrunk,
          iterations: i + 1,
          results
        };
      }
    }

    return {
      passed: true,
      iterations,
      results
    };
  }

  private async runPropertyTest<T>(
    property: (input: T) => boolean | Promise<boolean>,
    input: T
  ): Promise<boolean> {
    try {
      const result = await property(input);
      return Boolean(result);
    } catch {
      // If property throws an error, consider it a failure
      return false;
    }
  }

  private async shrinkCounterexample<T>(
    counterexample: T,
    property: (input: T) => boolean | Promise<boolean>,
    generator: Generator<T>
  ): Promise<T> {
    let current = counterexample;
    let shrunk = generator.shrink(current);

    // Try shrinking up to 10 times or until we find a minimal counterexample
    for (let i = 0; i < 10; i++) {
      if (await this.runPropertyTest(property, shrunk)) {
        // Shrunk value still passes, try shrinking further
        current = shrunk;
        shrunk = generator.shrink(current);
      } else {
        // Shrunk value fails, but might be shrinkable further
        const furtherShrunk = generator.shrink(shrunk);
        if (await this.runPropertyTest(property, furtherShrunk)) {
          // Further shrunk value passes, so current shrunk is minimal
          break;
        } else {
          // Further shrunk also fails, continue with it
          shrunk = furtherShrunk;
        }
      }
    }

    return current;
  }

  public addGenerator<T>(name: string, generator: Generator<T>): void {
    this.generators[name] = generator as Generator<unknown>;
  }

  public getGenerator<T>(name: string): Generator<T> | undefined {
    return this.generators[name] as Generator<T> | undefined;
  }

  public setSeed(seed: number): void {
    this.seed = seed;
    // Set random seed for reproducible results
    Math.random = this.seededRandom(seed);
  }

  private seededRandom(seed: number): () => number {
    let x = seed;
    return () => {
      x = (x * 9301 + 49297) % 233280;
      return x / 233280;
    };
  }

  public setMaxIterations(iterations: number): void {
    this.maxIterations = iterations;
  }

  // Utility method to create a generator from a function
  public static createGenerator<T>(
    generateFn: () => T,
    shrinkFn: (value: T) => T = (value) => value
  ): Generator<T> {
    return {
      generate: generateFn,
      shrink: shrinkFn
    };
  }

  // Predefined generators for common types
  public static integer(min = -100, max = 100): Generator<number> {
    return {
      generate: () => Math.floor(Math.random() * (max - min + 1)) + min,
      shrink: (value) => {
        if (value === 0) return 0;
        return value > 0 ? Math.max(min, value - 1) : Math.min(max, value + 1);
      }
    };
  }

  public static string(maxLength = 20): Generator<string> {
    return {
      generate: () => {
        const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        const length = Math.floor(Math.random() * maxLength) + 1;
        let result = '';
        for (let i = 0; i < length; i++) {
          result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
      },
      shrink: (value) => value.length > 1 ? value.slice(0, -1) : ''
    };
  }

  public static array<T>(elementGenerator: Generator<T>, maxLength = 10): Generator<T[]> {
    return {
      generate: () => {
        const length = Math.floor(Math.random() * maxLength);
        const result = [];
        for (let i = 0; i < length; i++) {
          result.push(elementGenerator.generate());
        }
        return result;
      },
      shrink: (value) => value.length > 0 ? value.slice(0, -1) : []
    };
  }

  public static oneOf<T>(values: T[]): Generator<T> {
    return {
      generate: () => values[Math.floor(Math.random() * values.length)],
      shrink: (value) => value // No meaningful shrink for oneOf
    };
  }
}

// Type for generator functions
export interface Generator<T> {
  generate(): T;
  shrink(value: T): T;
}