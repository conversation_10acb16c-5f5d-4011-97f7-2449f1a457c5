import mysql from 'mysql2/promise';

// Logger utility
const logger = {
  info: (msg: string, ...args: unknown[]) => import.meta.env.DEV && console.log(`[MySQLDataService] ${msg}`, ...args), // eslint-disable-line no-console
  warn: (msg: string, ...args: unknown[]) => import.meta.env.DEV && console.warn(`[MySQLDataService] ${msg}`, ...args), // eslint-disable-line no-console
  error: (msg: string, ...args: unknown[]) => console.error(`[MySQLDataService] ${msg}`, ...args) // eslint-disable-line no-console
};

const ERROR_PREFIX = 'Failed to ';
const UNKNOWN_ERROR = 'Unknown error';
const FILE_IMPORT_NOT_FOUND = 'File import not found';
const FAILED_TO_RETRIEVE_UPDATED = 'Failed to retrieve updated file import';

export interface FileImport {
  id: number;
  filename: string;
  file_type: string;
  upload_date: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  records_count: number;
  processed_records: number;
  errors_count: number;
  uploaded_by: string;
  file_size: string;
  created_at: string;
  updated_at: string;
}

const DB_CONFIG = {
  host: import.meta.env.VITE_DB_HOST || 'localhost',
  user: import.meta.env.VITE_DB_USER || 'root',
  password: import.meta.env.VITE_DB_PASSWORD || '',
  database: import.meta.env.VITE_DB_NAME || 'sgs_alumni_db',
  port: parseInt(import.meta.env.VITE_DB_PORT || '3306'),
  connectTimeout: 60000,
  acquireTimeout: 60000,
  timeout: 60000,
};

let pool: mysql.Pool | null = null;

const getPool = (): mysql.Pool => {
  if (!pool) pool = mysql.createPool(DB_CONFIG);
  return pool;
};

export class MySQLDataService {
  private static buildWhereClause(search?: string, status?: string): { whereClause: string; queryParams: string[] } {
    let where = '';
    const params: string[] = [];

    if (search) {
      where = 'WHERE (filename LIKE ? OR uploaded_by LIKE ?)';
      params.push(`%${search}%`, `%${search}%`);
    }

    if (status) {
      where += where ? ' AND status = ?' : 'WHERE status = ?';
      params.push(status);
    }

    return { whereClause: where, queryParams: params };
  }

  private static convertRowToFileImport(row: Record<string, unknown>): FileImport {
    return {
      id: row.id as number,
      filename: row.filename as string,
      file_type: row.file_type as string,
      upload_date: row.upload_date as string,
      status: row.status as FileImport['status'],
      records_count: row.records_count as number,
      processed_records: row.processed_records as number,
      errors_count: row.errors_count as number,
      uploaded_by: row.uploaded_by as string,
      file_size: row.file_size as string,
      created_at: row.created_at as string,
      updated_at: row.updated_at as string,
    };
  }

  private static async executeQuery(
    connection: mysql.PoolConnection,
    whereClause: string,
    queryParams: string[],
    pageSize: number,
    offset: number
  ): Promise<{ data: FileImport[]; total: number }> {
    const countQuery = `SELECT COUNT(*) as total FROM file_imports ${whereClause}`;
    const [countResult] = await connection.execute(countQuery, queryParams);
    const total = (countResult as Record<string, unknown>[])[0].total as number;

    const dataQuery = `SELECT * FROM file_imports ${whereClause} ORDER BY upload_date DESC LIMIT ? OFFSET ?`;
    const [rows] = await connection.execute(dataQuery, [...queryParams, pageSize, offset]);
    const data = (rows as Record<string, unknown>[]).map(row => this.convertRowToFileImport(row));

    return { data, total };
  }

  static async getFileImports(params: {
    page: number;
    pageSize: number;
    search?: string;
    status?: string;
  }): Promise<{
    data: FileImport[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }> {
    const connection = await getPool().getConnection();

    try {
      const { page, pageSize, search, status } = params;

      const { whereClause, queryParams } = this.buildWhereClause(search, status);
      const { data, total } = await this.executeQuery(connection, whereClause, queryParams, pageSize, page * pageSize);

      return { data, total, page, pageSize, totalPages: Math.ceil(total / pageSize) };

    } catch (error) {
      logger.error('Error fetching file imports:', error);
      throw new Error(`${ERROR_PREFIX}fetch file imports: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    } finally {
      connection.release();
    }
  }

  static async getFileImport(id: number): Promise<FileImport | null> {
    const connection = await getPool().getConnection();

    try {
      const [rows] = await connection.execute('SELECT * FROM file_imports WHERE id = ?', [id]);
      const resultRows = rows as Record<string, unknown>[];
      return resultRows.length ? this.convertRowToFileImport(resultRows[0]) : null;
    } catch (error) {
      logger.error('Error fetching file import:', error);
      throw new Error(`${ERROR_PREFIX}fetch file import: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    } finally {
      connection.release();
    }
  }

  static async createFileImport(fileImport: Omit<FileImport, 'id' | 'created_at' | 'updated_at'>): Promise<FileImport> {
    const connection = await getPool().getConnection();

    try {
      const now = new Date();
      const values = [
        fileImport.filename, fileImport.file_type, fileImport.upload_date, fileImport.status,
        fileImport.records_count, fileImport.processed_records, fileImport.errors_count,
        fileImport.uploaded_by, fileImport.file_size, now, now
      ];

      const [result] = await connection.execute(`
        INSERT INTO file_imports
        (filename, file_type, upload_date, status, records_count, processed_records, errors_count, uploaded_by, file_size, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`, values);

      return { id: (result as { insertId: number }).insertId, ...fileImport, created_at: now.toISOString(), updated_at: now.toISOString() };
    } catch (error) {
      logger.error('Error creating file import:', error);
      throw new Error(`${ERROR_PREFIX}create file import: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    } finally {
      connection.release();
    }
  }

  static async updateFileImport(id: number, updates: Partial<Omit<FileImport, 'id' | 'created_at'>>): Promise<FileImport> {
    const connection = await getPool().getConnection();

    try {
      const currentItem = await this.getFileImport(id);
      if (!currentItem) throw new Error(FILE_IMPORT_NOT_FOUND);

      const updateFields: string[] = [];
      const values: unknown[] = [];

      Object.entries(updates).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'created_at') {
          updateFields.push(`${key} = ?`);
          values.push(value);
        }
      });

      updateFields.push('updated_at = ?');
      values.push(new Date());

      await connection.execute(`UPDATE file_imports SET ${updateFields.join(', ')} WHERE id = ?`, [...values, id]);

      const updatedItem = await this.getFileImport(id);
      if (!updatedItem) throw new Error(FAILED_TO_RETRIEVE_UPDATED);
      return updatedItem;
    } catch (error) {
      logger.error('Error updating file import:', error);
      throw new Error(`${ERROR_PREFIX}update file import: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    } finally {
      connection.release();
    }
  }

  static async deleteFileImport(id: number): Promise<void> {
    const connection = await getPool().getConnection();

    try {
      await connection.execute('DELETE FROM file_imports WHERE id = ?', [id]);
    } catch (error) {
      logger.error('Error deleting file import:', error);
      throw new Error(`${ERROR_PREFIX}delete file import: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    } finally {
      connection.release();
    }
  }

  static async exportData(format: 'csv' | 'json', search?: string): Promise<unknown[][] | FileImport[]> {
    try {
      const allData = await this.getFileImports({ page: 0, pageSize: 10000, search });

      if (format === 'csv') {
        const headers = ['ID', 'Filename', 'Type', 'Upload Date', 'Status', 'Records', 'Processed', 'Errors', 'Uploaded By', 'File Size'];
        const rows = allData.data.map(item => [
          item.id, item.filename, item.file_type, item.upload_date, item.status,
          item.records_count, item.processed_records, item.errors_count, item.uploaded_by, item.file_size
        ]);
        return [headers, ...rows];
      }
      return allData.data;
    } catch (error) {
      logger.error('Error exporting data:', error);
      throw new Error(`${ERROR_PREFIX}export data: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    }
  }

  static async getStatistics(): Promise<{ totalImports: number; completedImports: number; failedImports: number; totalRecords: number }> {
    const connection = await getPool().getConnection();

    try {
      const [rows] = await connection.execute(`
        SELECT COUNT(*) as total_imports,
               SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_imports,
               SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_imports,
               SUM(records_count) as total_records
        FROM file_imports`);

      const stats = (rows as Record<string, unknown>[])[0];
      return {
        totalImports: (stats.total_imports as number) || 0,
        completedImports: (stats.completed_imports as number) || 0,
        failedImports: (stats.failed_imports as number) || 0,
        totalRecords: (stats.total_records as number) || 0
      };
    } catch (error) {
      logger.error('Error fetching statistics:', error);
      throw new Error(`${ERROR_PREFIX}fetch statistics: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    } finally {
      connection.release();
    }
  }

  static async testConnection(): Promise<boolean> {
    const connection = await getPool().getConnection();

    try {
      await connection.execute('SELECT 1');
      return true;
    } catch (error) {
      logger.error('Database connection failed:', error);
      return false;
    } finally {
      connection.release();
    }
  }
}

export const checkMySQLConfiguration = (): boolean =>
  !!(import.meta.env.VITE_DB_HOST && import.meta.env.VITE_DB_USER && import.meta.env.VITE_DB_PASSWORD && import.meta.env.VITE_DB_NAME);

export const getMySQLConfigStatus = () => ({
  hasHost: !!import.meta.env.VITE_DB_HOST,
  hasUser: !!import.meta.env.VITE_DB_USER,
  hasPassword: !!import.meta.env.VITE_DB_PASSWORD,
  hasDatabase: !!import.meta.env.VITE_DB_NAME,
  hasPort: !!import.meta.env.VITE_DB_PORT,
  isConfigured: checkMySQLConfiguration(),
  config: {
    host: import.meta.env.VITE_DB_HOST,
    user: import.meta.env.VITE_DB_USER,
    database: import.meta.env.VITE_DB_NAME,
    port: import.meta.env.VITE_DB_PORT,
  }
});

export const closeConnectionPool = async (): Promise<void> => {
  if (pool) {
    await pool.end();
    pool = null;
  }
};