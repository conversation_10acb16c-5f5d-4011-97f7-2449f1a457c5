import { useEffect, useState, ReactNode } from 'react';
import { ThemeConfiguration, ThemeName, ThemeContextType } from './types';
import { themes, availableThemes } from './configs';
import { injectCSSVariables, removeCSSVariables } from './tokens';
import { ThemeContext, logger } from './theme-utils';

// Local storage key for theme persistence
const THEME_STORAGE_KEY = 'sgs-theme-preference';

// Theme validation function
function validateTheme(themeName: string): themeName is ThemeName {
  return availableThemes.includes(themeName as ThemeName);
}

// Get initial theme from localStorage or system preference
function getInitialTheme(): ThemeName {
  // Check localStorage first
  const storedTheme = localStorage.getItem(THEME_STORAGE_KEY);
  if (storedTheme && validateTheme(storedTheme)) {
    return storedTheme as ThemeName;
  }

  // Check system preference for dark mode
  if (typeof window !== 'undefined' && window.matchMedia) {
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (prefersDark) {
      return 'dark';
    }
  }

  // Default to default theme
  return 'default';
}

// Custom hook for system theme change handling
function useSystemThemeChange(setCurrentTheme: (theme: ThemeName) => void) {
  useEffect(() => {
    if (typeof window === 'undefined') return;

    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleChange = (e: MediaQueryListEvent) => {
      // Only auto-switch if user hasn't manually set a preference
      const storedTheme = localStorage.getItem(THEME_STORAGE_KEY);
      if (!storedTheme) {
        setCurrentTheme(e.matches ? 'dark' : 'default');
      }
    };

    mediaQuery.addEventListener('change', handleChange);

    return () => {
      mediaQuery.removeEventListener('change', handleChange);
    };
  }, [setCurrentTheme]);
}

// Custom hook for theme configuration updates
function useThemeConfigUpdate(currentTheme: ThemeName, setThemeConfig: (config: ThemeConfiguration) => void) {
  useEffect(() => {
    const newTheme = themes[currentTheme];
    if (newTheme) {
      setThemeConfig(newTheme);
      // Inject CSS variables (both custom and shadcn/ui)
      removeCSSVariables();
      injectCSSVariables(newTheme);
      // Persist theme preference
      localStorage.setItem(THEME_STORAGE_KEY, currentTheme);
    }
  }, [currentTheme, setThemeConfig]);
}

interface ThemeProviderProps {
  children: ReactNode;
  defaultTheme?: ThemeName;
}

function ThemeProvider({ children, defaultTheme }: ThemeProviderProps) {
  const [currentTheme, setCurrentTheme] = useState<ThemeName>(() => {
    return defaultTheme || getInitialTheme();
  });

  const [theme, setThemeConfig] = useState<ThemeConfiguration>(() => {
    return themes[currentTheme];
  });

  // Use custom hooks for side effects
  useThemeConfigUpdate(currentTheme, setThemeConfig);
  useSystemThemeChange(setCurrentTheme);

  // Theme switching function with validation
  const setTheme = (themeName: ThemeName) => {
    if (!validateTheme(themeName)) {
      logger.error(`Invalid theme: ${themeName}. Available themes:`, availableThemes);
      return;
    }

    if (themeName === currentTheme) {
      return; // No change needed
    }

    setCurrentTheme(themeName);
  };

  const contextValue: ThemeContextType = {
    currentTheme,
    theme,
    setTheme,
    availableThemes,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
}

export default ThemeProvider;