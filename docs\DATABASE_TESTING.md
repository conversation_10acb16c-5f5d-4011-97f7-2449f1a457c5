# Database Connectivity Testing Guide

This guide covers comprehensive testing for AWS MySQL database connectivity in the SGS Alumni application.

## 🎯 Overview

The database testing suite provides multiple levels of testing:

1. **Unit Tests** - Mock-based tests for database service logic
2. **Integration Tests** - Real database connectivity tests
3. **Standalone Scripts** - Independent testing tools for debugging

## 🚀 Quick Start

### Basic Database Test
```bash
npm run test:db:quick
```

### Comprehensive Database Test
```bash
npm run test:db:full
```

### Unit Tests Only
```bash
npm run test:db:unit
```

### Integration Tests (Real Database)
```bash
npm run test:db:integration
```

## 📋 Test Types

### 1. Unit Tests (`database-connectivity.test.ts`)

**Purpose**: Test database service logic with mocked connections
**Location**: `src/__tests__/database-connectivity.test.ts`
**Run Command**: `npm run test:db:unit`

**What it tests**:
- Environment configuration validation
- Connection pool creation and management
- Error handling logic
- Service method behavior with mocked responses

**Example**:
```bash
npm run test:db:unit
```

### 2. Integration Tests (`database-integration.test.ts`)

**Purpose**: Test actual database connectivity and operations
**Location**: `src/__tests__/database-integration.test.ts`
**Run Command**: `npm run test:db:integration`

**What it tests**:
- Real AWS RDS MySQL connectivity
- Database schema validation
- Performance benchmarks
- Error handling with real network conditions

**Requirements**:
- Valid AWS RDS MySQL credentials in `.env`
- Network access to AWS RDS instance
- Set `VITEST_INTEGRATION_TESTS=true` environment variable

**Example**:
```bash
VITEST_INTEGRATION_TESTS=true npm run test:db:integration
```

### 3. Standalone Test Script (`test-database.js`)

**Purpose**: Independent database testing tool for debugging
**Location**: `scripts/test-database.js`
**Run Commands**: 
- `npm run test:db` (basic)
- `npm run test:db:quick` (quick tests only)
- `npm run test:db:full` (comprehensive with performance)

**What it tests**:
- Configuration validation
- Basic connectivity
- Database schema inspection
- Connection pool functionality
- Performance metrics

**Options**:
- `--quick`: Run only basic connectivity tests
- `--full`: Run comprehensive tests including performance
- `--verbose`: Show detailed output
- `--help`: Show help message

## 🔧 Configuration

### Environment Variables

The tests use the following environment variables (from `.env` and `.env.local`):

```env
# AWS RDS MySQL Configuration
DB_HOST=sgsbg-app-db.cj88ledblqs8.us-east-1.rds.amazonaws.com
DB_USER=sgsgita_alumni_user
DB_PASSWORD=your-password-here
DB_NAME=sgsgita_alumni
DB_PORT=3306

# Alternative Vite format (for frontend)
VITE_DB_HOST=sgsbg-app-db.cj88ledblqs8.us-east-1.rds.amazonaws.com
VITE_DB_USER=sgsgita_alumni_user
VITE_DB_PASSWORD=your-password-here
VITE_DB_NAME=sgsgita_alumni
VITE_DB_PORT=3306
```

### Test Configuration

Integration tests can be controlled with:

```env
# Enable integration tests
VITEST_INTEGRATION_TESTS=true

# Or set test environment
NODE_ENV=test-integration
```

## 📊 Test Results

### Successful Output Example

```
✅ Database configuration is valid
✅ Basic connectivity test passed (245ms)
✅ file_imports table exists and is accessible
✅ Connection pool test passed
✅ Performance test passed (avg: 156.30ms)

📊 Test Results Summary:
✅ Passed: 5
❌ Failed: 0
📈 Success Rate: 100.0%
```

### Common Issues and Solutions

#### 1. Connection Timeout
```
❌ Basic connectivity test failed: connect ETIMEDOUT
```
**Solution**: Check network connectivity and AWS security groups

#### 2. Authentication Failed
```
❌ Basic connectivity test failed: Access denied for user
```
**Solution**: Verify database credentials in `.env` file

#### 3. Database Not Found
```
❌ Basic connectivity test failed: Unknown database 'sgsgita_alumni'
```
**Solution**: Ensure database exists on AWS RDS instance

#### 4. Table Missing
```
⚠️ Expected table 'file_imports' not found
```
**Solution**: Create the required database schema

## 🛠️ Development Workflow

### 1. Before Making Database Changes
```bash
# Test current connectivity
npm run test:db:quick

# Run full test suite
npm run test:db:full
```

### 2. After Database Schema Changes
```bash
# Test schema validation
npm run test:db:integration

# Verify all functionality
npm run test:db:full
```

### 3. CI/CD Integration
```bash
# In CI pipeline - unit tests only
npm run test:db:unit

# In staging environment - integration tests
VITEST_INTEGRATION_TESTS=true npm run test:db:integration
```

## 🔍 Debugging

### Verbose Output
```bash
npm run test:db:full  # Already includes --verbose
```

### Manual Testing
```javascript
// In browser console or Node.js
import { runDatabaseIntegrationTests } from './src/__tests__/database-integration.test.ts'
await runDatabaseIntegrationTests()
```

### Connection Pool Debugging
```bash
# Check connection pool status
node -e "
import { getMySQLConfigStatus } from './src/lib/mysqlData.js'
console.log(getMySQLConfigStatus())
"
```

## 📈 Performance Benchmarks

Expected performance metrics:
- **Connection Time**: < 5 seconds
- **Query Response**: < 1 second average
- **Concurrent Connections**: 5+ simultaneous connections
- **Pool Management**: Proper connection reuse

## 🔒 Security Considerations

1. **Never commit database credentials** to version control
2. **Use environment variables** for all sensitive configuration
3. **Rotate credentials regularly** in production
4. **Use IAM roles** instead of access keys when possible
5. **Enable SSL/TLS** for database connections in production

## 📚 Related Documentation

- [AWS RDS MySQL Setup](../AWS_SETUP.md)
- [Environment Configuration](../ARCHITECTURE.md#environment-configuration)
- [Database Schema](../ARCHITECTURE.md#database-schema)
- [Deployment Guide](../docs/progress/phase-6/task-6.2-devops-pipeline.md)
