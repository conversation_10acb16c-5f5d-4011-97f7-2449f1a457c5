import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  UpdateI<PERSON><PERSON>ommand,
  DeleteItemCommand,
  GetItemCommand
} from "@aws-sdk/client-dynamodb";
import {
  marshall,
  unmarshall
} from "@aws-sdk/util-dynamodb";

// Logger utility for AWS Data Service
const logger = {
  info: (msg: string, ...args: unknown[]) => import.meta.env.DEV && console.log(`[AWSDataService] ${msg}`, ...args), // eslint-disable-line no-console
  warn: (msg: string, ...args: unknown[]) => console.warn(`[AWSDataService] ${msg}`, ...args), // eslint-disable-line no-console
  error: (msg: string, ...args: unknown[]) => console.error(`[AWSDataService] ${msg}`, ...args) // eslint-disable-line no-console
};

// File Import interface matching DynamoDB structure
export interface FileImport {
  id: string; // Partition key
  filename: string;
  file_type: string;
  upload_date: string; // ISO string
  status: 'pending' | 'processing' | 'completed' | 'failed';
  records_count: number;
  processed_records: number;
  errors_count: number;
  uploaded_by: string;
  file_size: string;
  created_at: string; // ISO string
  updated_at: string; // ISO string
}

// AWS Configuration
const AWS_REGION = import.meta.env.VITE_AWS_REGION || 'us-east-1';
const TABLE_NAME = import.meta.env.VITE_DYNAMODB_TABLE_NAME || 'sgs-alumni-file-imports-dev';

// Constants for error messages
const ERROR_PREFIX = 'Failed to';
const UNKNOWN_ERROR = 'Unknown error';

// Initialize DynamoDB client
const dynamoClient = new DynamoDBClient({
  region: AWS_REGION,
  credentials: {
    accessKeyId: import.meta.env.VITE_AWS_ACCESS_KEY_ID || '',
    secretAccessKey: import.meta.env.VITE_AWS_SECRET_ACCESS_KEY || '',
  },
});

const buildFilterExpression = (search?: string, status?: string) => {
  const values: Record<string, { S: string }> = {};
  const names: Record<string, string> = {};
  let filter = '';

  if (search) {
    filter = 'contains(#filename, :search) OR contains(#uploaded_by, :search)';
    names['#filename'] = 'filename';
    names['#uploaded_by'] = 'uploaded_by';
    values[':search'] = { S: search };
  }

  if (status) {
    if (filter) filter += ' AND ';
    filter += '#status = :status';
    names['#status'] = 'status';
    values[':status'] = { S: status };
  }

  return {
    filterExpression: filter || undefined,
    expressionAttributeValues: Object.keys(values).length ? values : undefined,
    expressionAttributeNames: Object.keys(names).length ? names : undefined
  };
};

const processScanResults = (items: FileImport[], page: number, pageSize: number) => {
  items.sort((a, b) => new Date(b.upload_date).getTime() - new Date(a.upload_date).getTime());
  const start = page * pageSize;
  const paginatedItems = items.slice(start, start + pageSize);

  return {
    data: paginatedItems,
    total: items.length,
    page,
    pageSize,
    totalPages: Math.ceil(items.length / pageSize)
  };
};

// AWS Service class for data operations
export class AWSDataService {
  private static readonly TABLE_NAME = TABLE_NAME;

  static async getFileImports(params: { page: number; pageSize: number; search?: string; status?: string }): Promise<{ data: FileImport[]; total: number; page: number; pageSize: number; totalPages: number }> {
    try {
      const { page, pageSize, search, status } = params;
      const filterData = buildFilterExpression(search, status);

      const scanCommand = new ScanCommand({
        TableName: this.TABLE_NAME,
        FilterExpression: filterData.filterExpression,
        ExpressionAttributeNames: filterData.expressionAttributeNames,
        ExpressionAttributeValues: filterData.expressionAttributeValues,
        Limit: pageSize,
      });

      const response = await dynamoClient.send(scanCommand);
      const items = response.Items?.map(item => unmarshall(item) as FileImport) || [];

      return processScanResults(items, page, pageSize);
    } catch (error) {
      logger.error('Error fetching file imports:', error);
      throw new Error(`${ERROR_PREFIX} fetch file imports: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    }
  }

  static async getFileImport(id: string): Promise<FileImport | null> {
    try {
      const getCommand = new GetItemCommand({ TableName: this.TABLE_NAME, Key: marshall({ id }) });
      const response = await dynamoClient.send(getCommand);
      return response.Item ? unmarshall(response.Item) as FileImport : null;
    } catch (error) {
      logger.error('Error fetching file import:', error);
      throw new Error(`${ERROR_PREFIX} fetch file import: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    }
  }

  static async createFileImport(fileImport: Omit<FileImport, 'id' | 'created_at' | 'updated_at'>): Promise<FileImport> {
    try {
      const now = new Date().toISOString();
      const id = `import_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const newFileImport: FileImport = { ...fileImport, id, created_at: now, updated_at: now };

      const putCommand = new PutItemCommand({
        TableName: this.TABLE_NAME,
        Item: marshall(newFileImport, { removeUndefinedValues: true })
      });

      await dynamoClient.send(putCommand);
      return newFileImport;
    } catch (error) {
      logger.error('Error creating file import:', error);
      throw new Error(`${ERROR_PREFIX} create file import: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    }
  }

  /**
   * Update an existing file import
   */
  static async updateFileImport(id: string, updates: Partial<Omit<FileImport, 'id' | 'created_at'>>): Promise<FileImport> {
    try {
      // First get the current item
      const currentItem = await this.getFileImport(id);
      if (!currentItem) {
        throw new Error('File import not found');
      }

      // Prepare update expression
      const updateExpressionParts: string[] = [];
      const expressionAttributeNames: Record<string, string> = {};
      const expressionAttributeValues: Record<string, string | number | boolean> = {};

      Object.entries(updates).forEach(([key, value]) => {
        if (key !== 'id' && key !== 'created_at') {
          updateExpressionParts.push(`#${key} = :${key}`);
          expressionAttributeNames[`#${key}`] = key;
          expressionAttributeValues[`:${key}`] = value;
        }
      });

      // Always update the updated_at timestamp
      updateExpressionParts.push('#updated_at = :updated_at');
      expressionAttributeNames['#updated_at'] = 'updated_at';
      expressionAttributeValues[':updated_at'] = new Date().toISOString();

      const updateExpression = `SET ${updateExpressionParts.join(', ')}`;

      const updateCommand = new UpdateItemCommand({
        TableName: this.TABLE_NAME,
        Key: marshall({ id }),
        UpdateExpression: updateExpression,
        ExpressionAttributeNames: expressionAttributeNames,
        ExpressionAttributeValues: marshall(expressionAttributeValues, { removeUndefinedValues: true }),
        ReturnValues: 'ALL_NEW'
      });

      const response = await dynamoClient.send(updateCommand);

      if (!response.Attributes) {
        throw new Error('Update failed - no attributes returned');
      }

      return unmarshall(response.Attributes) as FileImport;
    } catch (error) {
      logger.error('Error updating file import:', error);
      throw new Error(`${ERROR_PREFIX} update file import: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    }
  }

  static async deleteFileImport(id: string): Promise<void> {
    try {
      const deleteCommand = new DeleteItemCommand({ TableName: this.TABLE_NAME, Key: marshall({ id }) });
      await dynamoClient.send(deleteCommand);
    } catch (error) {
      logger.error('Error deleting file import:', error);
      throw new Error(`${ERROR_PREFIX} delete file import: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    }
  }

  static async exportData(format: 'csv' | 'json', search?: string): Promise<(string | number)[][] | FileImport[]> {
    try {
      const allData = await this.getFileImports({ page: 0, pageSize: 10000, search });

      if (format === 'csv') {
        const headers = ['ID', 'Filename', 'Type', 'Upload Date', 'Status', 'Records', 'Processed', 'Errors', 'Uploaded By', 'File Size'];
        const rows = allData.data.map(item => [
          item.id, item.filename, item.file_type, item.upload_date, item.status,
          item.records_count, item.processed_records, item.errors_count, item.uploaded_by, item.file_size
        ]);
        return [headers, ...rows];
      }
      return allData.data;
    } catch (error) {
      logger.error('Error exporting data:', error);
      throw new Error(`${ERROR_PREFIX} export data: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    }
  }

  static async getStatistics(): Promise<{ totalImports: number; completedImports: number; failedImports: number; totalRecords: number }> {
    try {
      const allData = await this.getFileImports({ page: 0, pageSize: 10000 });
      const data = allData.data;
      return {
        totalImports: allData.total,
        completedImports: data.filter(item => item.status === 'completed').length,
        failedImports: data.filter(item => item.status === 'failed').length,
        totalRecords: data.reduce((sum, item) => sum + item.records_count, 0)
      };
    } catch (error) {
      logger.error('Error fetching statistics:', error);
      throw new Error(`${ERROR_PREFIX} fetch statistics: ${error instanceof Error ? error.message : UNKNOWN_ERROR}`);
    }
  }
}

// Utility function to check AWS configuration
export const checkAWSConfiguration = (): boolean => {
  const accessKeyId = import.meta.env.VITE_AWS_ACCESS_KEY_ID;
  const secretAccessKey = import.meta.env.VITE_AWS_SECRET_ACCESS_KEY;
  const region = import.meta.env.VITE_AWS_REGION;

  return !!(accessKeyId && secretAccessKey && region);
};

// Utility function to get AWS configuration status
export const getAWSConfigStatus = () => {
  return {
    hasAccessKey: !!import.meta.env.VITE_AWS_ACCESS_KEY_ID,
    hasSecretKey: !!import.meta.env.VITE_AWS_SECRET_ACCESS_KEY,
    hasRegion: !!import.meta.env.VITE_AWS_REGION,
    tableName: import.meta.env.VITE_DYNAMODB_TABLE_NAME,
    isConfigured: checkAWSConfiguration()
  };
};