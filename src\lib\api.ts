export interface RawCsvUpload {
  id?: number;
  filename?: string;
  file_name?: string;
  file_type?: string;
  type?: string;
  upload_date?: string;
  created_at?: string;
  status?: string;
  records_count?: number;
  total_records?: number;
  processed_records?: number;
  errors_count?: number;
  error_count?: number;
  uploaded_by?: string;
  user?: string;
  file_size?: string;
  size?: string;
}

export interface ApiOptions {
  params?: Record<string, string | number | boolean>;
}

export interface ApiResponse {
  [key: string]: unknown;
}

export const api = {
  get: (url: string, options?: ApiOptions): Promise<ApiResponse> => {
    const params = options?.params ? new URLSearchParams(options.params as Record<string, string>).toString() : '';
    const fullUrl = params ? `${url}?${params}` : url;
    return fetch(fullUrl).then(res => res.json());
  },
  post: (url: string, data: Record<string, unknown>): Promise<ApiResponse> => fetch(url, {
    method: 'POST',
    body: JSON.stringify(data),
    headers: { 'Content-Type': 'application/json' }
  }).then(res => res.json()),
  put: (url: string, data: Record<string, unknown>): Promise<ApiResponse> => fetch(url, {
    method: 'PUT',
    body: JSON.stringify(data),
    headers: { 'Content-Type': 'application/json' }
  }).then(res => res.json()),
  delete: (url: string): Promise<ApiResponse> => fetch(url, { method: 'DELETE' }).then(res => res.json()),
}

import axios from 'axios';

export async function getData(skip: number = 0, limit: number = 100, search?: string): Promise<unknown[]> {
  const response = await axios.get('/data', {
    params: { skip, limit, search }
  });
  return response.data;
}

export default api;