// Advanced Testing Framework Types and Interfaces

export interface Mutant {
  id: string;
  originalCode: string;
  mutatedCode: string;
  operator: string;
  location: {
    file: string;
    line: number;
    column: number;
  };
  equivalent?: boolean;
}

export interface MutationTestResults {
  score: number;
  killedMutants: number;
  survivedMutants: Mutant[];
  equivalentMutants: Mutant[];
  totalMutants: number;
}

export interface TestCaseResult {
  input: unknown;
  passed: boolean;
  counterexample?: unknown;
}

export interface PropertyTestResult {
  passed: boolean;
  counterexample?: unknown;
  iterations: number;
  results: TestCaseResult[];
}

export interface Screenshot {
  name: string;
  data: Buffer;
  timestamp: Date;
}

export interface ImageComparison {
  component: string;
  passed: boolean;
  diffPercentage: number;
  diffImage?: Buffer;
}

export interface VisualTestResults {
  totalTests: number;
  passed: number;
  failed: number;
  failures: Array<{
    component: string;
    diffPercentage: number;
    diffImage?: Buffer;
  }>;
}

export interface PerformanceMetrics {
  [key: string]: number;
}

export interface PerformanceRegression {
  metric: string;
  baselineValue: number;
  currentValue: number;
  changePercentage: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface PerformanceTestResults {
  baseline: PerformanceMetrics;
  current: PerformanceMetrics;
  regressions: PerformanceRegression[];
  improvements: PerformanceRegression[];
  overallTrend: 'improving' | 'stable' | 'degrading';
  recommendations: string[];
}

export interface ComponentAnalysis {
  componentName: string;
  importPath: string;
  props: Array<{
    name: string;
    type: string;
    required: boolean;
  }>;
  methods: string[];
  dependencies: string[];
}

export interface TestScenario {
  description: string;
  testCode: string;
  priority: 'high' | 'medium' | 'low';
}

export interface GeneratedTests {
  testCode: string;
  coverage: number;
  scenarios: TestScenario[];
  validation: {
    syntaxValid: boolean;
    importsValid: boolean;
    coverage: number;
  };
}

export interface CodeChange {
  file: string;
  type: 'added' | 'modified' | 'deleted';
  lines: number[];
  content: string;
}

export interface TestRelevance {
  test: TestCase;
  relevance: number;
  dependencies: string[];
}

export interface PrioritizedTests {
  highPriority: TestCase[];
  mediumPriority: TestCase[];
  lowPriority: TestCase[];
  executionOrder: TestCase[];
}

export interface TestCase {
  id: string;
  name: string;
  file: string;
  category: string;
  priority: 'high' | 'medium' | 'low';
  lastRun?: Date;
  duration?: number;
}

export interface RiskAssessment {
  overall: 'low' | 'medium' | 'high' | 'critical';
  factors: Array<{
    type: string;
    severity: number;
    description: string;
  }>;
}

export interface TestRunner {
  run(test: TestCase): Promise<boolean>;
  runMultiple(tests: TestCase[]): Promise<TestCaseResult[]>;
}

export interface AIEngine {
  generateTestScenarios(analysis: ComponentAnalysis): Promise<TestScenario[]>;
  fillTemplate(template: string, data: Record<string, unknown>): Promise<string>;
}

export interface CodeAnalyzer {
  analyze(filePath: string): Promise<ComponentAnalysis>;
}

export interface RiskAnalyzer {
  assessRisk(changes: CodeChange[]): Promise<RiskAssessment>;
}

export interface ChangeAnalyzer {
  calculateRelevance(test: TestCase, changes: CodeChange[]): Promise<number>;
  analyzeDependencies(test: TestCase): Promise<string[]>;
}

export interface ScreenshotEngine {
  capture(selector: string, name: string): Promise<Screenshot>;
}

export interface ImageComparisonEngine {
  compare(image1: Buffer, image2: Buffer): Promise<ImageComparison>;
}

export interface BenchmarkEngine {
  measurePerformance(fn: () => void | Promise<void>, iterations?: number): Promise<PerformanceMetrics>;
}

export interface MetricsCollector {
  collect(): Promise<PerformanceMetrics>;
}

export interface PropertyGenerators {
  [key: string]: {
    generate(): any; // eslint-disable-line @typescript-eslint/no-explicit-any
    shrink(value: any): any; // eslint-disable-line @typescript-eslint/no-explicit-any
  };
}